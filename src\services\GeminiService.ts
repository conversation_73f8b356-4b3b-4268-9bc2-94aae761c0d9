import { GoogleGenerativeAI, GenerationConfig } from '@google/generative-ai';
import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 獲取當前文件的目錄路徑
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加載環境變數
dotenv.config();

/**
 * Gemini AI 服務
 * 與 Google Gemini AI API 進行交互，處理自然語言請求
 */
export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private defaultConfig: GenerationConfig;
  private systemPrompt: APPPromptResult | null = null;
  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || '';
    if (!apiKey) {
      throw new Error('未設置 GEMINI_API_KEY 環境變數');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });    this.defaultConfig = {
      temperature: 0.1,  // 降低創造性，提高準確性
      topP: 0.8,         // 降低隨機性
      topK: 20,          // 減少候選詞數量
      maxOutputTokens: 2048,
    };
    
  // 不再加載固定的系統提示，改為使用動態生成的 APPprompt
    console.log('Gemini 服務已初始化，將使用動態生成的 APPprompt');
  }

  /**
   * 從 BDD 規範生成 APPprompt
   * @param bddSpec BDD 規範
   * @param menuData 可選的菜單數據
   * @param language 語言設定
   */
  async generateFromBDD(bddSpec: BDDSpec, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    // 根據語言設定提示詞語言
    let languageInstruction = '';
    if (language === 'en-US') {
      languageInstruction = 'Please generate the APPprompt content in English. All prompt text, feature descriptions, scenarios, and conditions should be in English.';
    } else if (language === 'ja-JP') {
      languageInstruction = 'APPpromptの内容を日本語で生成してください。プロンプトテキスト、機能説明、シナリオ、条件はすべて日本語で記述してください。';
    } else {
      languageInstruction = '請以繁體中文生成APPprompt內容。所有提示詞文字、功能描述、場景和條件都應該使用繁體中文。';
    }
    
    // 構建提示詞
    let prompt = `
    ${languageInstruction}
    
    以下是一個 BDD（行為驅動開發）規範，請將其轉換為 APPprompt 格式，用於自然語言點餐系統：

    Feature: ${bddSpec.feature}
    Scenario: ${bddSpec.scenario}
    `;

    if (bddSpec.given && bddSpec.given.length > 0) {
      prompt += `\nGiven: ${bddSpec.given.join('\n')}\n`;
    }
    
    if (bddSpec.when && bddSpec.when.length > 0) {
      prompt += `\nWhen: ${bddSpec.when.join('\n')}\n`;
    }
    
    if (bddSpec.then && bddSpec.then.length > 0) {
      prompt += `\nThen: ${bddSpec.then.join('\n')}\n`;
    }

    // 如果有菜單數據，添加到提示詞中
    if (menuData && menuData.categories && menuData.categories.length > 0) {
      prompt += `\n當前菜單包含以下項目：\n`;

      // 為每個類別添加菜單項目
      menuData.categories.forEach(category => {
        // 根據語言選擇類別名稱
        let categoryName = category.name_zh || '未分類';
        if (language === 'en-US' && category.name_en) {
          categoryName = category.name_en;
        } else if (language === 'ja-JP' && category.name_jp) {
          categoryName = category.name_jp;
        }
        
        prompt += `\n${categoryName}：\n`;
        
        category.items.forEach(item => {
          // 根據語言選擇項目名稱和價格
          let itemName = item.name_zh || '';
          let itemPrice = item.price;
          let currency = 'NT$';
          
          if (language === 'en-US' && item.name_en) {
            itemName = item.name_en;
          } else if (language === 'ja-JP') {
            if (item.name_jp) itemName = item.name_jp;
            if (item.price_jp) {
              itemPrice = item.price_jp;
              currency = '¥';
            }
          }
          
          // 添加圖片URL信息（如果有的話）
          let itemInfo = `- ${itemName} (ID: ${item.id}): ${currency}${itemPrice}`;
          if (item.image_url) {
            itemInfo += ` [圖片: ${item.image_url}]`;
          }
          prompt += itemInfo + '\n';
        });
      });
        prompt += `\n請確保生成的 APPprompt 包含完整的菜單數據，應該能夠從菜單中選擇項目並完成點餐。`;
      prompt += `\nmenu 字段應該是一個數組，其中每個元素包含兩個屬性：category（類別名稱）和 items（該類別下的所有菜單項數組）。`;
      prompt += `\n不要使用註釋 /* ... menu data as provided ... */ 代替實際菜單數據，必須返回完整的菜單項數組。`;
      // 根據語言調整菜單項範例
      if (language === 'en-US') {
        prompt += `\n菜單項應包含 id、name_en、price 等必要信息，例如：
        menu: [
          {
            "category": "Fried Chicken",
            "items": [
              {"id": "1", "name_en": "Crispy Chicken (Spicy)", "price": 124},
              {"id": "2", "name_en": "Colonel's Crispy Chicken (Mild)", "price": 124}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      } else if (language === 'ja-JP') {
        prompt += `\n菜單項應包含 id、name_jp、price_jp 等必要信息，例如：
        menu: [
          {
            "category": "フライドチキン",
            "items": [
              {"id": "1", "name_jp": "クリスピーチキン（辛口）", "price_jp": 450},
              {"id": "2", "name_jp": "カーネルクリスピーチキン（マイルド）", "price_jp": 450}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      } else {
        prompt += `\n菜單項應包含 id、name_zh、price 等必要信息，例如：
        menu: [
          {
            "category": "炸雞類",
            "items": [
              {"id": "1", "name_zh": "咔啦脆雞（辣）", "price": 124},
              {"id": "2", "name_zh": "上校薄脆雞（不辣）", "price": 124}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      }
    }

    prompt += `
    APPprompt 格式應該是一個簡潔的自然語言提示，並包含所有必要的參數。

    🚨🚨🚨 重要：生成的 APPprompt 必須包含強制 JSON 回應格式要求 🚨🚨🚨

    在生成的 prompt 中，必須包含以下 JSON 格式要求：

    "🚨🚨🚨 強制回應格式 - 必須嚴格遵守 🚨🚨🚨

    每次回應都必須包含兩部分：
    1. 自然語言回應
    2. JSON 格式的訂單摘要（用 ORDER_JSON_START 和 ORDER_JSON_END 包圍）

    範例（必須完全按照此格式）：
    \"好的！您點了大麥克套餐，1份總計 ¥143。

    ORDER_JSON_START
    {
      \"items\": [
        {
          \"name\": \"大麥克套餐\",
          \"price\": 143,
          \"quantity\": 1,
          \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?\"
        }
      ],
      \"total\": 143
    }
    ORDER_JSON_END\"

    🔥 JSON 格式要求：
    - name: 餐點名稱（使用菜單中的確切名稱）
    - price: 單價（使用菜單中的確切價格）
    - quantity: 數量
    - image_url: 圖片URL（從菜單的 \"image\" 欄位複製完整URL）
    - total: 總金額

    🔥 如果沒有包含 ORDER_JSON_START...ORDER_JSON_END 格式，回應將被視為無效！"

    請按照 JSON 格式回傳結果，包括 prompt（提示詞）和 parameters（參數）。
    參數應該包含從 BDD 規範中提取的特徵、場景、條件和預期結果。
    `;
    
    // 調用 Gemini API
    const result = await this.generateWithGemini(prompt);
      try {
      // 嘗試解析返回結果為 JSON
      const parsedResult = this.extractJsonFromResponse(result);
      
      // 确保结果中包含完整的菜单数据
      const resultWithMenu = this.ensureMenuDataInResult(parsedResult, menuData);
      
      return {
        prompt: resultWithMenu.prompt || result,
        parameters: resultWithMenu.parameters || {
          feature: bddSpec.feature,
          scenario: bddSpec.scenario
        },
        metadata: {
          source: 'bdd',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    } catch (error) {
      console.error('解析 Gemini 回應失敗:', error);
      
      // 如果解析失敗，則回傳原始回應作為提示詞
      return {
        prompt: result,
        parameters: {
          feature: bddSpec.feature,
          scenario: bddSpec.scenario
        },
        metadata: {
          source: 'bdd',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    }
  }

  /**
   * 從 AAprompt 格式生成 APPprompt
   * @param aaPrompt AAprompt 格式
   * @param menuData 可選的菜單數據
   * @param language 語言設定
   */
  async generateFromAA(aaPrompt: AAPrompt, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    // 根據語言設定提示詞語言
    let languageInstruction = '';
    if (language === 'en-US') {
      languageInstruction = 'Please generate the APPprompt content in English. All prompt text, role descriptions, actions, and constraints should be in English.';
    } else if (language === 'ja-JP') {
      languageInstruction = 'APPpromptの内容を日本語で生成してください。プロンプトテキスト、役割説明、アクション、制約はすべて日本語で記述してください。';
    } else {
      languageInstruction = '請以繁體中文生成APPprompt內容。所有提示詞文字、角色描述、行動和限制條件都應該使用繁體中文。';
    }
    
    // 構建提示詞
    let prompt = `
    ${languageInstruction}
    
    請將以下 AAprompt 格式的提示轉換為 APPprompt 格式，用於自然語言點餐系統：

    作為：${aaPrompt.actor}
    行動：${aaPrompt.action}
    `;

    if (aaPrompt.context) {
      prompt += `\n情境：${aaPrompt.context}\n`;
    }
    
    if (aaPrompt.constraints && aaPrompt.constraints.length > 0) {
      prompt += `\n限制條件：\n${aaPrompt.constraints.join('\n')}\n`;
    }
    
    // 如果有菜單數據，添加到提示詞中
    if (menuData && menuData.categories && menuData.categories.length > 0) {
      prompt += `\n當前菜單包含以下項目：\n`;

      // 為每個類別添加菜單項目
      menuData.categories.forEach(category => {
        // 根據語言選擇類別名稱
        let categoryName = category.name_zh || '未分類';
        if (language === 'en-US' && category.name_en) {
          categoryName = category.name_en;
        } else if (language === 'ja-JP' && category.name_jp) {
          categoryName = category.name_jp;
        }
        
        prompt += `\n${categoryName}：\n`;
        
        category.items.forEach(item => {
          // 根據語言選擇項目名稱和價格
          let itemName = item.name_zh || '';
          let itemPrice = item.price;
          let currency = 'NT$';
          
          if (language === 'en-US' && item.name_en) {
            itemName = item.name_en;
          } else if (language === 'ja-JP') {
            if (item.name_jp) itemName = item.name_jp;
            if (item.price_jp) {
              itemPrice = item.price_jp;
              currency = '¥';
            }
          }
          
          // 添加圖片URL信息（如果有的話）
          let itemInfo = `- ${itemName} (ID: ${item.id}): ${currency}${itemPrice}`;
          if (item.image_url) {
            itemInfo += ` [圖片: ${item.image_url}]`;
          }
          prompt += itemInfo + '\n';
        });
      });
        prompt += `\n請確保生成的 APPprompt 與上述菜單項目兼容，應該能夠從菜單中選擇項目並完成點餐。`;
      prompt += `\nmenu 字段應該是一個數組，其中每個元素包含兩個屬性：category（類別名稱）和 items（該類別下的所有菜單項數組）。`;
      prompt += `\n不要使用註釋 /* ... menu data as provided ... */ 代替實際菜單數據，必須返回完整的菜單項數組。`;
      // 根據語言調整菜單項範例
      if (language === 'en-US') {
        prompt += `\n菜單項應包含 id、name_en、price 等必要信息，例如：
        menu: [
          {
            "category": "Fried Chicken",
            "items": [
              {"id": "1", "name_en": "Crispy Chicken (Spicy)", "price": 124},
              {"id": "2", "name_en": "Colonel's Crispy Chicken (Mild)", "price": 124}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      } else if (language === 'ja-JP') {
        prompt += `\n菜單項應包含 id、name_jp、price_jp 等必要信息，例如：
        menu: [
          {
            "category": "フライドチキン",
            "items": [
              {"id": "1", "name_jp": "クリスピーチキン（辛口）", "price_jp": 450},
              {"id": "2", "name_jp": "カーネルクリスピーチキン（マイルド）", "price_jp": 450}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      } else {
        prompt += `\n菜單項應包含 id、name_zh、price 等必要信息，例如：
        menu: [
          {
            "category": "炸雞類",
            "items": [
              {"id": "1", "name_zh": "咔啦脆雞（辣）", "price": 124},
              {"id": "2", "name_zh": "上校薄脆雞（不辣）", "price": 124}
              // 其他餐點...
            ]
          },
          // 其他類別...
        ]
        `;
      }
    }

    prompt += `
    APPprompt 格式應該是一個簡潔的自然語言提示，並包含所有必要的參數。

    🚨🚨🚨 重要：生成的 APPprompt 必須包含強制 JSON 回應格式要求 🚨🚨🚨

    在生成的 prompt 中，必須包含以下 JSON 格式要求：

    "🚨🚨🚨 強制回應格式 - 必須嚴格遵守 🚨🚨🚨

    每次回應都必須包含兩部分：
    1. 自然語言回應
    2. JSON 格式的訂單摘要（用 ORDER_JSON_START 和 ORDER_JSON_END 包圍）

    範例（必須完全按照此格式）：
    \"好的！您點了大麥克套餐，1份總計 ¥143。

    ORDER_JSON_START
    {
      \"items\": [
        {
          \"name\": \"大麥克套餐\",
          \"price\": 143,
          \"quantity\": 1,
          \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?\"
        }
      ],
      \"total\": 143
    }
    ORDER_JSON_END\"

    🔥 JSON 格式要求：
    - name: 餐點名稱（使用菜單中的確切名稱）
    - price: 單價（使用菜單中的確切價格）
    - quantity: 數量
    - image_url: 圖片URL（從菜單的 \"image\" 欄位複製完整URL）
    - total: 總金額

    🔥 如果沒有包含 ORDER_JSON_START...ORDER_JSON_END 格式，回應將被視為無效！"

    請按照 JSON 格式回傳結果，包括 prompt（提示詞）和 parameters（參數）。
    參數應該包含角色、行動、情境和限制條件。
    `;
    
    // 調用 Gemini API
    const result = await this.generateWithGemini(prompt);
      try {
      // 嘗試解析返回結果為 JSON
      const parsedResult = this.extractJsonFromResponse(result);
      
      // 確保結果中包含完整的菜單數據
      const resultWithMenu = this.ensureMenuDataInResult(parsedResult, menuData);
      
      return {
        prompt: resultWithMenu.prompt || result,
        parameters: resultWithMenu.parameters || {
          actor: aaPrompt.actor,
          action: aaPrompt.action,
          context: aaPrompt.context,
          constraints: aaPrompt.constraints
        },
        metadata: {
          source: 'aaprompt',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    } catch (error) {
      console.error('解析 Gemini 回應失敗:', error);
      
      // 如果解析失敗，則回傳原始回應作為提示詞
      return {
        prompt: result,
        parameters: {
          actor: aaPrompt.actor,
          action: aaPrompt.action,
          context: aaPrompt.context,
          constraints: aaPrompt.constraints
        },
        metadata: {
          source: 'aaprompt',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    }
  }
  /**
   * 處理自然語言點餐請求（支援會話隔離）
   * @param input 自然語言輸入
   * @param menuData 菜單數據
   * @param appPrompt 可選的預生成 APPprompt（優先使用）
   * @param sessionId 會話ID（用於日誌追蹤和會話隔離）
   */  async processNaturalLanguageOrder(input: string, menuData?: MenuData | null, appPrompt?: string, sessionId?: string): Promise<string> {
    const logPrefix = sessionId ? `[Session:${sessionId}]` : '[NoSession]';
    let prompt = '';

    console.log(`${logPrefix} === 開始處理自然語言點餐 ===`);
    console.log(`${logPrefix} 顧客輸入:`, input);
    console.log(`${logPrefix} 菜單數據可用:`, !!menuData);
    console.log(`${logPrefix} APPprompt 可用:`, !!appPrompt);

    // 加強調試：記錄接收到的菜單數據（僅當沒有 APPprompt 時）
    if (menuData && menuData.categories && !appPrompt) {
      console.log(`${logPrefix} === Gemini Service 菜單數據調試 ===`);
      console.log(`${logPrefix} 接收到菜單數據，包含 ${menuData.categories.length} 個類別`);
      menuData.categories.forEach(category => {
        console.log(`${logPrefix} 類別: ${category.name_zh}, 項目數: ${category.items.length}`);
        category.items.forEach(item => {
          if (item.name_zh && item.name_zh.includes('麥香魚')) {
            console.log(`${logPrefix} *** 麥香魚套餐價格: ${item.price} ***`);
          }
        });
      });
      console.log(`${logPrefix} ================================`);
    }
      // 如果有預生成的 APPprompt，完全依賴 APPprompt 的系統提示和菜單數據
    if (appPrompt && appPrompt.trim()) {
      console.log(`${logPrefix} === 使用 APPprompt 作為系統提示 ===`);
      console.log(`${logPrefix} APPprompt 長度:`, appPrompt.length);

      // 嘗試解析 APPprompt 並檢查菜單數據
      try {
        const appPromptData = JSON.parse(appPrompt);
        if (appPromptData.parameters && appPromptData.parameters.menu) {
          console.log(`${logPrefix} APPprompt 包含菜單數據:`);
          appPromptData.parameters.menu.forEach((category: any) => {
            console.log(`${logPrefix}   - ${category.category}: ${category.items.length} 項餐點`);
            category.items.forEach((item: any) => {
              if (item.name_zh && item.name_zh.includes('麥香魚')) {
                console.log(`${logPrefix}     *** 麥香魚套餐價格確認: NT$${item.price} ***`);
              }
            });
          });
        }
      } catch (e) {
        console.log(`${logPrefix} 無法解析 APPprompt JSON，但仍將使用其內容`);
      }
      
      // 解析 APPprompt 以確定語言和貨幣設置
      let language = 'zh-TW';
      let currency = 'NT$';
      let customerLabel = '顧客說';
      let instructionLabel = '系統執行指令';

      try {
        const appPromptData = JSON.parse(appPrompt);

        // 檢測語言 - 改進語言檢測邏輯
        const promptText = appPromptData.prompt || '';
        if (promptText.includes('日本語') ||
            promptText.includes('ファーストフード') ||
            promptText.includes('注文受付システム') ||
            promptText.includes('あなたは自然言語で注文を受け付ける') ||
            promptText.includes('顧客の注文を理解し')) {
          language = 'ja-JP';
          currency = '¥';
          customerLabel = 'お客様のご注文';
          instructionLabel = 'システム実行指示';
        } else if (promptText.includes('English') ||
                   promptText.includes('natural language ordering') ||
                   promptText.includes('fast food restaurant') ||
                   promptText.includes('order processing system')) {
          language = 'en-US';
          currency = '$';
          customerLabel = 'Customer said';
          instructionLabel = 'System Instructions';
        }

        // 檢查菜單中的價格格式
        if (appPromptData.parameters && appPromptData.parameters.menu) {
          const firstItem = appPromptData.parameters.menu[0]?.items?.[0];
          if (firstItem) {
            if (firstItem.price_jp) {
              currency = '¥';
            } else if (firstItem.price_usd) {
              currency = '$';
            }
          }
        }
      } catch (e) {
        console.log(`${logPrefix} 無法解析 APPprompt 語言設置，使用預設中文`);
      }

      // 根據語言設置構建適當的指令
      if (language === 'ja-JP') {
        prompt = `${appPrompt}

【${customerLabel}】
お客様：「${input}」

【${instructionLabel}】
あなたは専門的な注文アシスタントです。以下の厳格なルールに従ってお客様のニーズを処理してください：

🎯 **コアルール**
1. **上記のシステムプロンプトで提供されたメニューデータと価格のみを使用**
2. **外部記憶、仮定、推測価格の使用を絶対禁止**
3. **すべての価格は上記のmenuデータ構造から取得**

💰 **価格処理**
- 注文リクエストの場合、各料理の実際の価格を表示
- 合計金額を計算して表示
- 形式：「料理名 ${currency}価格」

🗣️ **重要：必ず日本語で回答してください**
- すべての回答は日本語で行う
- 中国語や英語は使用しない
- 自然で親しみやすい日本語の口調でお客様に応答する
- 価格情報が正確であることを確認する

お客様への回答は必ず日本語で行ってください。`;
      } else if (language === 'en-US') {
        prompt = `${appPrompt}

【${customerLabel}】
Customer: "${input}"

【${instructionLabel}】
You are a professional ordering assistant. Please process customer needs according to the following strict rules:

🎯 **Core Rules**
1. **Only use menu data and prices provided in the above system prompt**
2. **Absolutely prohibit using any external memory, assumptions, or guessed prices**
3. **All prices must come from the above menu data structure**

💰 **Price Processing**
- For ordering requests, display the actual price of each dish
- Calculate and display total amount
- Format: "Dish Name ${currency}Price"

🗣️ **Important: You must respond in English only**
- All responses must be in English
- Do not use Chinese or Japanese
- Respond to customers with a natural and friendly English tone
- Ensure price information is accurate

Please respond to the customer in English only.`;
      } else {
        // 中文版本（保持原有邏輯）
        prompt = `${appPrompt}

【${customerLabel}】
顧客說：「${input}」

【${instructionLabel}】
你是專業的點餐助手，請按照以下嚴格規則處理顧客需求：

🎯 **核心規則**
1. **只能使用上述系統提示中提供的菜單數據和價格**
2. **絕對禁止使用任何外部記憶、假設或猜測的價格**
3. **所有價格必須來自上述 menu 數據結構**

💰 **價格處理**
- 如果是點餐請求，必須顯示每個餐點的實際價格
- 計算並顯示總金額
- 格式：「餐點名稱 ${currency}價格」

🗣️ **重要：必須使用繁體中文回答**
- 所有回答都必須使用繁體中文
- 不要使用日文或英文
- 用自然、友善的中文語調回應顧客
- 確保價格信息準確無誤

請必須使用繁體中文回應顧客。`;
      }
      
      console.log('================================');
    }
    // 如果沒有 APPprompt 但有菜單數據，構建基本提示
    else if (menuData && menuData.categories && menuData.categories.length > 0) {
      prompt = `你是一個專業的點餐系統助手。請幫助顧客完成點餐。

顧客說：「${input}」

【重要】以下是完整的菜單價格表，請嚴格按照這些價格回應，絕對不要使用其他價格：

可用菜單：
`;
      
      // 特別標注重要項目的價格
      let menuText = '';
      menuData.categories.forEach(category => {
        menuText += `\n【${category.name_zh || category.name_en || '未分類'}】\n`;
        
        category.items.forEach(item => {
          const priceText = `NT$${item.price}`;
          let itemInfo = `${item.name_zh || item.name_en || ''} (ID: ${item.id}): ${priceText}`;

          // 添加圖片URL信息（如果有的話）
          if (item.image_url) {
            itemInfo += ` [圖片: ${item.image_url}]`;
          }

          // 特別標記麥香魚套餐的價格
          if (item.name_zh && item.name_zh.includes('麥香魚')) {
            menuText += `- ★★★ ${itemInfo} ★★★\n`;
          } else {
            menuText += `- ${itemInfo}\n`;
          }
        });
      });

      prompt += menuText;
        prompt += `

🚨🚨🚨 強制回應格式 - 必須嚴格遵守 🚨🚨🚨

每次回應都必須包含兩部分：
1. 自然語言回應
2. JSON 格式的訂單摘要（用 ORDER_JSON_START 和 ORDER_JSON_END 包圍）

範例（必須完全按照此格式）：
"好的！您點了大麥克套餐，1份總計 ¥143。

ORDER_JSON_START
{
  "items": [
    {
      "name": "大麥克套餐",
      "price": 143,
      "quantity": 1,
      "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?"
    }
  ],
  "total": 143
}
ORDER_JSON_END"

【嚴格遵守規則 - 絕對不允許違反】
1. 如果是點餐請求，識別具體的餐點項目和數量
2. 從上方菜單中查找EXACT PRICE（精確價格）並計算總金額
3. 必須在回應中明確顯示每個餐點的價格和總金額（格式：NT$XXX）
4. 🚨 重要警告：絕對不要使用菜單以外的價格！麥香魚套餐價格是NT$117，不是NT$139！🚨
5. 🚨 禁止使用任何假設價格、記憶中的價格或常見價格！只能使用上方菜單中列出的價格！🚨
6. 🔥 必須在每次回應最後包含 ORDER_JSON_START...ORDER_JSON_END 格式的 JSON！
7. JSON 中的 image_url 必須從菜單的 "image" 欄位複製完整URL
8. 不要要求額外信息，直接確認訂單和實際價格
9. 請用自然、親切的語調回應顧客

價格驗證檢查清單：
✓ 麥香魚套餐 = NT$117 （不是NT$139）
✓ 大麥克套餐 = NT$143
✓ 雙層吉士漢堡套餐 = NT$137
✓ 烤雞堡套餐 = NT$148

🔥 如果沒有包含 ORDER_JSON_START...ORDER_JSON_END 格式，回應將被視為無效！`;
    }
    // 如果都沒有，使用最基本的提示
    else {
      prompt = `你是一個點餐系統助手。顧客說：「${input}」

由於目前沒有可用的菜單資料，請禮貌地告知顧客需要先上傳菜單，或請他們稍後再試。`;
    }

    console.log('=== 發送給 Gemini 的完整 Prompt ===');
    console.log(prompt.substring(0, 1500) + '...(truncated)');
    console.log('=====================================');    const result = await this.generateWithGemini(prompt);
    
    console.log('=== Gemini 原始回應 ===');
    console.log(result);
    console.log('=======================');

    // 嘗試解析 JSON 格式的訂單信息
    const orderJson = this.extractOrderJson(result);
    if (orderJson) {
      console.log('=== 解析到的訂單 JSON ===');
      console.log(JSON.stringify(orderJson, null, 2));
      console.log('========================');

      // 不再將圖片信息添加到自然語言回應中，讓前端直接使用 JSON 數據
      // 只返回原始回應，前端會解析 JSON 部分來顯示圖片
      return result;
    }

    // 如果沒有 JSON，使用原有的價格修正方法
    const correctedResult = this.validateAndCorrectPrices(result, menuData);

    if (correctedResult !== result) {
      console.log('=== 修正後的回應 ===');
      console.log(correctedResult);
      console.log('==================');
    }

    return correctedResult;
  }
  /**
   * 解析自然語言，嘗試轉化為 APPprompt
   * @param input 自然語言輸入
   * @param menuData 可選的菜單數據
   * @param sessionId 會話ID（可選）
   */
  async parseNaturalLanguageToAppPrompt(input: string, menuData?: MenuData | null, sessionId?: string): Promise<APPPromptResult> {
    // 先嘗試處理自然語言點餐，不傳遞 appPrompt（讓它使用菜單數據）
    const orderResponse = await this.processNaturalLanguageOrder(input, menuData, undefined, sessionId);
    
    // 將回應包裝成 APPPromptResult 格式
    return {
      prompt: orderResponse,
      parameters: { originalInput: input },
      metadata: {
        source: 'natural' as const,
        generatedAt: new Date(),
        aiGenerated: true
      }
    };
  }
    /**
   * 使用 Gemini 生成文本
   * @param prompt 提示詞
   * @param config 生成配置
   */
  private async generateWithGemini(prompt: string, config: GenerationConfig = this.defaultConfig): Promise<string> {
    try {
      console.log('正在調用 Gemini API...');
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: config
      });
      
      const responseText = result.response.text();
      console.log('Gemini API 調用成功，收到回應');
      return responseText;
    } catch (error) {
      console.error('Gemini API 調用失敗:', error);
      // 當 API 調用失敗時，返回一個特定格式的錯誤信息，而不是拋出異常
      // 這樣可以確保上層函數能夠繼續處理，並向用戶返回有用的信息
      return JSON.stringify({
        prompt: "無法從 Gemini 獲取回應",
        parameters: {
          error: error instanceof Error ? error.message : '未知錯誤',
          timestamp: new Date().toISOString()
        }
      });
    }
  }/**
   * 從回應中提取 JSON
   * @param response API 回應
   */
  private extractJsonFromResponse(response: string): any {
    try {
      // 首先嘗試解析整個回應
      return JSON.parse(response);
    } catch (e) {
      console.log('整個回應不是有效的 JSON，開始進行預處理和提取...');
      console.log('原始回應:', response.substring(0, 100) + '...');
      
      // 記錄原始錯誤
      console.log('原始JSON解析錯誤:', e instanceof Error ? e.message : '未知錯誤');
      
      // 第一步：修復常見的 JSON 語法問題
      let preprocessed = response
        // 去除註釋
        .replace(/\/\*[\s\S]*?\*\//g, '[]')
        .replace(/\/\/.*$/gm, '')
        // 修復未加引號的鍵名
        .replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":')
        // 處理 Python 的 None/True/False
        .replace(/:\s*None\b/g, ': null')
        .replace(/:\s*True\b/g, ': true')
        .replace(/:\s*False\b/g, ': false')
        // 修復漏掉的逗號
        .replace(/"\s*\n\s*"/g, '",\n"')
        .replace(/\}\s*\n\s*\{/g, '},\n{')
        // 修復尾部逗號
        .replace(/,(\s*[\}\]])/g, '$1')
        // 替換 "id" 為 ID 的情況 (常見的未加引號問題)
        .replace(/:\s*id\b/g, ': "id"');
      
      console.log('預處理完成，嘗試解析...');
      
      try {
        // 嘗試解析預處理後的響應
        return JSON.parse(preprocessed);
      } catch (preprocessError) {
        console.log('預處理後仍無法解析，進行進階提取:', preprocessError instanceof Error ? preprocessError.message : '未知錯誤');
      }
      
      // 嘗試從代碼塊中提取 JSON
      const codeBlockRegex = /```(?:json)?\s*(\{[\s\S]*?\})\s*```/;
      const codeBlockMatch = response.match(codeBlockRegex);
      
      if (codeBlockMatch && codeBlockMatch[1]) {
        console.log('發現代碼塊，嘗試解析...');
        const codeBlockContent = codeBlockMatch[1];
        
        // 對代碼塊內容進行相同的預處理
        const processedCodeBlock = codeBlockContent
          .replace(/\/\*[\s\S]*?\*\//g, '[]')
          .replace(/\/\/.*$/gm, '')
          .replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":')
          .replace(/:\s*None\b/g, ': null')
          .replace(/:\s*True\b/g, ': true')
          .replace(/:\s*False\b/g, ': false')
          .replace(/"\s*\n\s*"/g, '",\n"')
          .replace(/\}\s*\n\s*\{/g, '},\n{')
          .replace(/,(\s*[\}\]])/g, '$1')
          .replace(/:\s*id\b/g, ': "id"');
        
        try {
          return JSON.parse(processedCodeBlock);
        } catch (codeBlockError) {
          console.log('代碼塊解析失敗:', codeBlockError instanceof Error ? codeBlockError.message : '未知錯誤');
        }
      }
      
      // 使用正則表達式查找所有可能的 JSON 對象
      const jsonRegex = /(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/g;
      const matches = response.match(jsonRegex);
      
      if (matches && matches.length > 0) {
        console.log(`找到 ${matches.length} 個可能的 JSON 對象，開始嘗試解析...`);
        
        // 嘗試解析每個匹配
        for (const match of matches) {
          const processedMatch = match
            .replace(/\/\*[\s\S]*?\*\//g, '[]')
            .replace(/\/\/.*$/gm, '')
            .replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":')
            .replace(/:\s*None\b/g, ': null')
            .replace(/:\s*True\b/g, ': true')
            .replace(/:\s*False\b/g, ': false')
            .replace(/"\s*\n\s*"/g, '",\n"')
            .replace(/\}\s*\n\s*\{/g, '},\n{')
            .replace(/,(\s*[\}\]])/g, '$1')
            .replace(/:\s*id\b/g, ': "id"');
          
          try {
            const result = JSON.parse(processedMatch);
            if (result && (result.prompt || result.parameters)) {
              console.log('成功找到並解析符合條件的 JSON 對象');
              return result;
            }
          } catch (matchError) {
            // 繼續嘗試下一個匹配
          }
        }
        
        // 如果沒有找到符合條件的對象，嘗試返回第一個可以解析的對象
        for (const match of matches) {
          const processedMatch = match
            .replace(/\/\*[\s\S]*?\*\//g, '[]')
            .replace(/\/\/.*$/gm, '')
            .replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":')
            .replace(/:\s*None\b/g, ': null')
            .replace(/:\s*True\b/g, ': true')
            .replace(/:\s*False\b/g, ': false')
            .replace(/"\s*\n\s*"/g, '",\n"')
            .replace(/\}\s*\n\s*\{/g, '},\n{')
            .replace(/,(\s*[\}\]])/g, '$1')
            .replace(/:\s*id\b/g, ': "id"');
          
          try {
            console.log('嘗試解析第一個可解析的對象');
            return JSON.parse(processedMatch);
          } catch (firstMatchError) {
            // 繼續嘗試下一個匹配
          }
        }
      }
      
      // 如果所有方法都失敗，則構建一個基本的對象返回
      console.log('無法從回應中提取有效的 JSON，構建基本對象');
      return {
        prompt: response,
        parameters: {}
      };
    }
  }  /**
   * 確保結果中包含完整的菜單數據
   * @param result 解析後的結果
   * @param menuData 菜單數據
   */
  private ensureMenuDataInResult(result: any, menuData?: MenuData | null): any {
    // 如果沒有菜單數據，直接返回結果
    if (!menuData || !menuData.categories || menuData.categories.length === 0) {
      return result;
    }
    
    // 確保 result 和 parameters 對象存在
    if (!result) result = {};
    if (!result.parameters) result.parameters = {};
    
    // 檢查菜單格式是否需要替換
    let needReplacement = true;
    
    if (result.parameters.menu && Array.isArray(result.parameters.menu)) {
      // 檢查菜單格式是否正確且完整
      const hasValidFormat = result.parameters.menu.every((category: any) => {
        // 檢查每個類別是否有必要的屬性
        if (!category || typeof category !== 'object') return false;
        if (!category.category || typeof category.category !== 'string') return false;
        if (!category.items || !Array.isArray(category.items)) return false;
        
        // 檢查每個菜單項是否有必要的屬性
        return category.items.every((item: any) => {
          return item && 
                 typeof item === 'object' && 
                 item.id && 
                 (item.name_zh || item.name_en || item.name_jp) && 
                 (typeof item.price === 'number' || typeof item.price_jp === 'number');
        });
      });
      
      // 檢查菜單是否包含完整的類別數量
      const expectedCategoryCount = menuData.categories.length;
      const actualCategoryCount = result.parameters.menu.length;
      const hasCompleteCategories = actualCategoryCount >= expectedCategoryCount;
      
      // 計算總項目數量
      const expectedItemCount = menuData.categories.reduce((total, cat) => total + cat.items.length, 0);
      const actualItemCount = result.parameters.menu.reduce((total: number, cat: any) => total + (cat.items ? cat.items.length : 0), 0);
      const hasCompleteItems = actualItemCount >= expectedItemCount * 0.9; // 允許 10% 的誤差
      
      // 只有格式正確且數據完整時才不需要替換
      if (hasValidFormat && hasCompleteCategories && hasCompleteItems) {
        needReplacement = false;
        console.log(`菜單數據完整且格式正確 - 類別: ${actualCategoryCount}/${expectedCategoryCount}, 項目: ${actualItemCount}/${expectedItemCount}`);
      } else {
        console.log(`菜單數據不完整或格式錯誤 - 類別: ${actualCategoryCount}/${expectedCategoryCount}, 項目: ${actualItemCount}/${expectedItemCount}, 格式正確: ${hasValidFormat}`);
        needReplacement = true;
      }
    } else {
      console.log('菜單數據不存在或不是數組，需要創建');
    }    
    // 如果需要替換菜單數據
    if (needReplacement) {
      try {
        // 創建符合預期格式的菜單數組
        const formattedMenu = menuData.categories.map(category => {
          const categoryName = category.name_zh || category.name_en || '未分類';
          
          // 創建該分類下的所有菜單項
          const items = category.items.map(item => {
            const menuItem: any = {
              id: item.id,
              name_zh: item.name_zh || '',
              name_en: item.name_en || '',
              price: item.price,
              category: categoryName
            };
            
            // 添加日文欄位（如果存在）
            if (item.name_jp) {
              menuItem.name_jp = item.name_jp;
            }
            if (item.price_jp) {
              menuItem.price_jp = item.price_jp;
            }

            // 添加圖片URL（如果存在）
            if (item.image_url) {
              menuItem.image_url = item.image_url;
            }
            
            return menuItem;
          });
          
          // 返回帶有分類和項目的對象
          return {
            category: categoryName,
            items: items
          };
        });
        
        // 將組織好的菜單數據設置到結果中
        result.parameters.menu = formattedMenu;
        
        // 計算總項目數
        const totalItems = formattedMenu.reduce((total, cat) => total + cat.items.length, 0);
        console.log(`已強制替換菜單數據，包含 ${formattedMenu.length} 個類別，總計 ${totalItems} 個項目`);
        
        // 記錄每個類別的項目數量
        formattedMenu.forEach(cat => {
          console.log(`  - ${cat.category}: ${cat.items.length} 個項目`);
        });
        
      } catch (error) {
        console.error('替換菜單數據時出錯:', error);
        // 出錯時創建一個空菜單，確保不會因為菜單格式錯誤而中斷流程
        result.parameters.menu = [];
      }
    }
    
    return result;
  }
  /**
   * 驗證和修正 AI 回應中的價格，並添加圖片信息
   * @param response AI 原始回應
   * @param menuData 菜單數據
   * @returns 修正後的回應
   */
  private validateAndCorrectPrices(response: string, menuData?: MenuData | null): string {
    if (!menuData || !menuData.categories) {
      return response;
    }

    // 創建價格和圖片映射表
    const itemMap = new Map<string, {price: number, imageUrl?: string}>();
    menuData.categories.forEach(category => {
      category.items.forEach(item => {
        const itemInfo = {
          price: item.price,
          imageUrl: item.image_url
        };
        if (item.name_zh) {
          itemMap.set(item.name_zh.toLowerCase(), itemInfo);
        }
        if (item.name_en) {
          itemMap.set(item.name_en.toLowerCase(), itemInfo);
        }
      });
    });

    let correctedResponse = response;
    let hasCorrected = false;

    // 檢查並修正常見的錯誤價格
    const commonErrors = [
      { incorrect: 'NT$139', correct: 'NT$117', itemKeywords: ['麥香魚', 'filet-o-fish'] },
      { incorrect: 'NT$150', correct: 'NT$143', itemKeywords: ['大麥克', 'big mac'] },
      { incorrect: 'NT$185', correct: 'NT$157', itemKeywords: ['四盎司牛肉堡套餐', 'quarter pounder'] },
      { incorrect: 'NT$154', correct: 'NT$174', itemKeywords: ['麥克雞塊(10塊)套餐', 'mcnuggets (10 pieces)'] },
      { incorrect: 'NT$55', correct: 'NT$105', itemKeywords: ['墨西哥莎莎堡', 'mexican salsa burger'] },
    ];

    commonErrors.forEach(error => {
      if (correctedResponse.includes(error.incorrect)) {
        // 檢查上下文是否包含相關餐點
        const contextMatch = error.itemKeywords.some(keyword => 
          correctedResponse.toLowerCase().includes(keyword)
        );
        
        if (contextMatch) {
          console.log(`🔧 修正錯誤價格: ${error.incorrect} -> ${error.correct}`);
          correctedResponse = correctedResponse.replace(new RegExp(error.incorrect, 'g'), error.correct);
          hasCorrected = true;
        }
      }
    });

    // 添加圖片信息：檢查回應中提到的餐點名稱，為每個餐點添加圖片URL
    console.log('🔍 開始檢查並添加圖片信息...');
    itemMap.forEach((itemInfo, itemName) => {
      const lowerResponse = correctedResponse.toLowerCase();
      const lowerItemName = itemName.toLowerCase();

      if (lowerResponse.includes(lowerItemName) && itemInfo.imageUrl) {
        // 檢查該餐點名稱後是否已經有圖片信息
        const itemWithImageRegex = new RegExp(`${itemName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}.*?\\[圖片:`, 'gi');

        if (!itemWithImageRegex.test(correctedResponse)) {
          // 找到餐點名稱並在其後添加圖片信息
          const itemNameRegex = new RegExp(`(${itemName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
          correctedResponse = correctedResponse.replace(itemNameRegex, (match) => {
            console.log(`🖼️ 已添加圖片信息: ${match} -> ${itemInfo.imageUrl}`);
            return `${match} [圖片: ${itemInfo.imageUrl}]`;
          });
        }
      }

      // 尋找價格模式（在同一個 forEach 循環內）
      if (lowerResponse.includes(lowerItemName)) {
        const priceRegex = /NT\$(\d+)/g;
        let match;

        while ((match = priceRegex.exec(correctedResponse)) !== null) {
          const foundPrice = parseInt(match[1]);
          if (foundPrice !== itemInfo.price && Math.abs(foundPrice - itemInfo.price) > 20) {
            // 如果價格差異超過20元，認為可能是錯誤的
            // 移除價格檢測警告輸出
            // console.log(`⚠️ 檢測到可能錯誤的價格: ${itemName} -> NT$${foundPrice}, 正確價格應為 NT$${itemInfo.price}`);
          }
        }
      }
    });

    if (hasCorrected) {
      console.log('✅ 已修正 AI 回應中的價格錯誤');
    }

    return correctedResponse;
  }

  /**
   * 從 Gemini 回應中提取 JSON 格式的訂單信息
   * @param response Gemini 的回應
   * @returns 解析後的訂單對象或 null
   */
  private extractOrderJson(response: string): any | null {
    try {
      // 查找 ORDER_JSON_START 和 ORDER_JSON_END 之間的內容
      const startMarker = 'ORDER_JSON_START';
      const endMarker = 'ORDER_JSON_END';

      const startIndex = response.indexOf(startMarker);
      const endIndex = response.indexOf(endMarker);

      if (startIndex === -1 || endIndex === -1) {
        console.log('未找到 JSON 標記');
        return null;
      }

      const jsonStr = response.substring(startIndex + startMarker.length, endIndex).trim();
      console.log('提取的 JSON 字符串:', jsonStr);

      const orderData = JSON.parse(jsonStr);
      return orderData;
    } catch (error) {
      console.log('JSON 解析失敗:', error);
      return null;
    }
  }


}
