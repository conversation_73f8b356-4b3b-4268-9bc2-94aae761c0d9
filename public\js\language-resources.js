/**
 * 語言資源文件
 * 包含繁體中文、英文和日文的翻譯
 */

// 語言資源對象
const languageResources = {
    'zh-TW': { // 繁體中文
        // 頁面標題
        'page_title': '自然語言點餐系統',
        'page_subtitle': 'Architecture Agent 智慧零售應用實作',
        
        // 標籤頁
        'tab_menu_management': '菜單管理',
        'tab_bdd_editor': 'BDD 編輯器',
        'tab_aa_prompt_editor': 'AAprompt 編輯器',
        'tab_app_prompt_generator': 'APPprompt 生成器',
        'tab_natural_order': '自然語言點餐',
        
        // 菜單管理
        'menu_management_title': '菜單上傳與管理',
        'restaurant_id_label': '餐廳 ID',
        'restaurant_id_placeholder': '請輸入餐廳識別碼',
        'drop_file_text': '拖曳檔案到此處或點擊選擇檔案',
        'supported_formats': '支援的檔案格式：CSV, Excel, JSON',
        'select_file_btn': '選擇檔案',
        'auto_detect_category': '自動偵測分類',
        'upload_menu_btn': '上傳菜單',
        'processing_text': '處理中，請稍候...',
        
        // BDD 編輯器
        'bdd_editor_title': 'BDD 編輯器',
        'bdd_text_label': '行為驅動規範 (BDD)',
        'bdd_text_placeholder': '例如：\nFeature: 自然語言點餐\n  As a customer\n  I want to order food using natural language\n  So that I can place my order quickly and intuitively\n\nScenario: 顧客使用文字輸入點餐...',
        'save_bdd_btn': '儲存',
        'saving_bdd': '儲存中...',
        'bdd_save_success': 'BDD內容已成功儲存',
        'bdd_content_required': '請輸入BDD內容',
        'bdd_save_failed': '儲存BDD失敗',
        
        // AAprompt 編輯器
        'aa_editor_title': 'AAprompt 編輯器',
        'aa_text_label': 'Actor-Action 提示詞',
        'aa_text_placeholder': '格式: 作為[角色]，[動作]，在[情境]的情況下，[限制條件]\n\n例如：\n作為自然語言點餐系統，解析顧客的點餐請求並識別菜單項目，在快速服務餐廳的情況下，確保準確識別菜單項目和數量，處理特殊要求和修改請求',
        'save_aaprompt_btn': '儲存',
        'cannot_open_file_dialog': '無法開啟檔案選擇對話框，請嘗試點擊「選擇檔案」按鈕',
        'file_selection_error': '選擇檔案後發生錯誤，請重試',
        'saving_aaprompt': '儲存中...',
        'aaprompt_save_success': 'AAprompt內容已成功儲存',
        'aaprompt_content_required': '請輸入AAprompt內容',
        'aaprompt_save_failed': '儲存AAprompt失敗',
        
        // APPprompt 生成器
        'app_generator_title': 'APPprompt 生成器',
        'app_generator_btn': '生成 APPprompt',
        'download_appprompt_btn': '下載完整APPprompt',
        'generating': '生成中...',
        'generate_appprompt': '生成 APPprompt',
        'generating_appprompt_using': '正在生成 APPprompt，使用',
        'generating_appprompt_wait': '正在生成 APPprompt，請稍候...',
        'content': '內容',
        'analyzing_with_gemini': '正在使用 USI AIOS 分析並生成 APPprompt...',
        'generated_appprompt': '生成的 APPprompt',
        'generated_appprompt_empty': '生成的 APPprompt 為空',
        'appprompt_generation_error': '生成APPprompt錯誤',
        'generating_with_gemini': '正在使用 USI AIOS 生成 APPprompt...',
        'gemini_generation_success': 'USI AIOS 已成功生成 APPprompt',
        'appprompt_generation_success': 'APPprompt 已成功生成',
        'appprompt_generation_failed': '生成 APPprompt 失敗',
        'generate_appprompt_first': '請先生成 APPprompt',
        'found_generate_btn': '找到 generate-btn 按鈕，綁定點擊事件',
        'generate_btn_not_found': '找不到 generate-btn 按鈕',
        'auto_generating_appprompt': '正在自動生成USI AIOS Agent，請稍候...',
        'appprompt_auto_generated': 'USI AIOS Agent已自動生成，現在可以開始點餐了！',
        'auto_generate_failed': '自動生成USI AIOS Agent失敗，請手動切換到開發頁面生成',
        'please_upload_menu_first': '請先上傳菜單後再使用點餐功能',
        'appprompt_ready': 'USI AIOS Agent 已準備就緒，可以開始點餐',
        'appprompt_not_ready': 'USI AIOS Agent 尚未準備就緒',
        'system_initializing': '系統正在初始化...',
        'auto_generate_on_switch': '切換到此頁籤時將自動生成USI AIOS Agent...',
        'ready_to_order': '您現在可以開始使用自然語言點餐功能！',
        
        // 自然語言點餐
        'natural_order_title': '快速點餐系統',
        'restaurant_selector_label': '選擇餐廳',
        'order_input_label': '請輸入您的點餐需求',
        'order_input_placeholder': '例如：我要一個大麥克套餐，飲料換成可樂，薯條要大包的',
        'submit_order_btn': '處理訂單',
        'clear_input_btn': '清除輸入',
        'voice_input_btn': '語音輸入',
        'modify_order_btn': '修改訂單',
        'clear_order_btn': '清空訂單',
        'confirm_order_btn': '確認訂單',
        'restart_order_btn': '重新點餐',
        
        // USI AIOS 訂單助手
        'gemini_assistant_title': 'USI AIOS 訂單助手',
        'ai_processing_title': 'USI AIOS 智能分析中',
        'step_receive_order': '接收訂單',
        'step_ai_analysis': 'AI 語義分析',
        'step_menu_matching': '菜單匹配',
        'step_generate_result': '生成結果',
        'ai_thinking_text': '正在理解您的需求...',
        'preparing_results': '準備為您呈現結果...',
        
        // 訂單確認模態框
        'order_complete_title': '訂單完成!',
        'order_complete_message': '感謝您的訂購！您的餐點正在準備中。',
        'order_summary_title': '訂單摘要:',
        'order_summary': '訂單摘要',
        'quantity': '數量',
        'total': '總計',
        'total_amount': '總金額',
        'close_btn': '關閉',
        'new_order_btn': '點新的一單',
        
        // 通知訊息
        'success_title': '成功！',
        'error_title': '錯誤！',
        'no_matches_title': '無法獲取 AI 回應',
        'no_matches_text': '請重新嘗試您的點餐請求',
        'order_confirm_error': '訂單確認功能暫時無法使用，請稍後再試',
        'no_order_content': '無法獲取訂單內容',
        'order_summary_failed': '訂單摘要生成失敗，請檢查控制台日誌',
        'select_items_first': '請先選擇商品加入購物車',
        'order_processing': '訂單處理中...',
        'order_submitted': '訂單已成功提交！',
        'order_submit_failed': '訂單提交失敗，請重試',
        'gemini_analysis_success': 'USI AIOS Agent 已成功分析您的訂單',
        'bdd_save_success': 'BDD 儲存成功！',
        'bdd_save_failed': 'BDD 儲存失敗',
        'aaprompt_save_success': 'AAprompt 儲存成功！',
        'aaprompt_save_failed': 'AAprompt 儲存失敗',
        'error_title': '錯誤！',
        'save_bdd_btn': '儲存',
        'save_aaprompt_btn': '儲存',
        'refresh_language_content': '刷新語言內容',
        'confirm_refresh_bdd': '確定要將BDD編輯器內容更新為 {language} 的預設內容嗎？\n\n這將會覆蓋目前的內容。',
        'confirm_refresh_aaprompt': '確定要將AAprompt編輯器內容更新為 {language} 的預設內容嗎？\n\n這將會覆蓋目前的內容。',
        'refreshing': '刷新中...',
        'refresh_success_bdd': 'BDD編輯器已更新為{language}的預設內容',
        'refresh_success_aaprompt': 'AAprompt編輯器已更新為{language}的預設內容',
        'refresh_failed_bdd': '刷新BDD編輯器語言內容失敗',
        'refresh_failed_aaprompt': '刷新AAprompt編輯器語言內容失敗',
        'unable_load_default_content': '無法載入預設內容',
        'auto_switch_bdd_confirm': '檢測到您正在切換語系到 {language}。\n是否要將BDD編輯器內容更新為新語系的預設內容？\n\n點擊「確定」更新，點擊「取消」保持現有內容。',
        'auto_switch_aaprompt_confirm': '檢測到您正在切換語系到 {language}。\n是否要將AAprompt編輯器內容更新為新語系的預設內容？\n\n點擊「確定」更新，點擊「取消」保持現有內容。',
        'cannot_open_file_dialog': '無法開啟檔案選擇對話框，請嘗試點擊「選擇檔案」按鈕',
        'file_selection_error': '選擇檔案後發生錯誤，請重試',
        
        // Toast 消息
        'new_order_started': '已開始新的訂單',
        'order_cleared': '訂單已清空',
        'enter_modification_content': '請輸入您想要修改的內容',
        'cart_empty': '購物車是空的，請先選擇餐點',
        'select_valid_items': '請先選擇有效的餐點再結帳',
        'item_increased': '已增加 {item} 的數量',
        'item_decreased': '已減少 {item} 的數量',
        'item_removed': '已移除 {item}',
        'total_corrected': '已修正總金額從 NT${original} 到 NT${final}',
        'menu_upload_success': '菜單上傳成功！',
        'processed_items': '已成功處理 {count} 個菜單項目',
        'menu_upload_failed': '菜單上傳失敗',
        'upload_error': '上傳過程中發生錯誤',
        'file_selected': '已選擇檔案',
        'file_size': '檔案大小',
        'file_selected_success': '檔案已成功選擇，請點擊「上傳菜單」按鈕完成上傳',
        'file_name': '檔案名稱',
        'restaurant_id': '餐廳 ID',
        'total_items': '總商品數',
        'category_info': '分類資訊',
        'items_unit': '項',
        'info_title': '資訊',
        'warning_title': '警告',
        
        // 菜單項目顯示
        'menu_item_name': '商品名稱',
        'menu_item_price': '價格',
        'menu_item_category': '分類',
        
        // 編輯器錯誤訊息
        'aaprompt_syntax_error': 'AAprompt 語法錯誤',
        
        // 菜單預覽
        'menu_preview': '菜單預覽',
        'version': '版本',
        'please_select_file': '請選擇要上傳的檔案',
        
        // 語言選單
        'language_btn': '中文',
        'language_zh': '中文',
        'language_en': 'English',
        'language_ja': '日本語',
        
        // 新增的錯誤訊息和處理文字
        'gemini_api_connection_failed': 'Gemini API 連線失敗',
        'check_network_connection': '請檢查網路連線或稍後再試',
        'processing_please_wait': '處理中，請稍候...',
        'ai_analyzing_order': '正在使用人工智慧分析您的訂單，請稍候',
        'gemini_ai_analyzing_order': '正在使用USI AIOS人工智慧分析您的訂單，請稍候',
        'ai_analyzed_order': '人工智慧已為您分析訂單：',
        'enter_modification_request': '請輸入您想要修改的內容',
        'order_cleared_success': '訂單已清空',
        'order_confirmation_unavailable': '訂單確認功能暫時無法使用',
        'order_confirmation_modal_not_found': '找不到訂單確認模態框',
        'unable_to_get_order_info': '無法獲取訂單信息',
        'please_confirm_price': '請確認價格',
        'confirm_order': '確認訂單',
        'please_confirm_your_order': '請確認您的訂單',
        'speech_permission_needed': '需要語音權限',
        'enable_speech': '啟用語音',
        'speech_enabled': '語音已啟用',
        'speech_disabled': '語音已禁用',
        'close': '關閉',
        'modify_order': '修改訂單',
        'clear_order': '清空訂單',
        'gemini_ai_response': 'USI AIOS 回應',
        'order_result_container_not_found': '找不到訂單結果容器',
        'displaying_gemini_response': '顯示 USI AIOS 回應',
        'set_gemini_order_data': '已設置 window.currentGeminiOrderData',
        'using_fixed_checkout': '使用修復版結帳功能處理 Gemini 回應',
        'fixed_checkout_not_found': '找不到修復版結帳功能，使用備用確認對話框',
        'showing_order_confirmation': '顯示訂單確認對話框',
        'order_confirmation_modal_shown': '訂單確認模態框已顯示',
        'showing_toast_notification': '顯示 Toast 通知',
        'toast_container_created': '已創建 Toast 容器',

        // 會話狀態面板
        'session_id': '會話ID',
        'menu_status': '菜單',
        'language_status': '語言',
        'clear_session_btn': '清除會話',
        'status_loaded': '已載入',
        'status_not_loaded': '未載入',
        'status_generated': '已生成',
        'status_not_generated': '未生成',
        'no_appprompt_available': '沒有可用的 APPprompt，請先上傳菜單並生成 APPprompt',

        // 錯誤訊息
        'please_enter_order_request': '請輸入您的餐點需求',
        'session_manager_not_initialized': '會話管理器未初始化，請重新載入頁面',
        'usi_aios_response_format_error': 'USI AIOS 回應格式異常',
        'confirm_clear_session': '確定要清除當前會話嗎？這將清除所有已上傳的菜單和生成的 APPprompt。',
        'session_cleared': '會話已清除'
    },
    'en-US': { // 英文
        // 頁面標題
        'page_title': 'Natural Language Ordering System',
        'page_subtitle': 'Architecture Agent Smart Retail Application',
        
        // 標籤頁
        'tab_menu_management': 'Menu Management',
        'tab_bdd_editor': 'BDD Editor',
        'tab_aa_prompt_editor': 'AAprompt Editor',
        'tab_app_prompt_generator': 'APPprompt Generator',
        'tab_natural_order': 'Natural Language Order',
        
        // 菜單管理
        'menu_management_title': 'Menu Upload & Management',
        'restaurant_id_label': 'Restaurant ID',
        'restaurant_id_placeholder': 'Please enter restaurant identifier',
        'drop_file_text': 'Drag files here or click to select',
        'supported_formats': 'Supported formats: CSV, Excel, JSON',
        'select_file_btn': 'Select File',
        'auto_detect_category': 'Auto-detect Categories',
        'upload_menu_btn': 'Upload Menu',
        'processing_text': 'Processing, please wait...',
        
        // BDD 編輯器
        'bdd_editor_title': 'BDD Editor',
        'bdd_text_label': 'Behavior-Driven Development (BDD)',
        'bdd_text_placeholder': 'Example:\nFeature: Natural Language Ordering\n  As a customer\n  I want to order food using natural language\n  So that I can place my order quickly and intuitively\n\nScenario: Customer orders using text input...',
        'save_bdd_btn': 'Save',
        'saving_bdd': 'Saving...',
        'bdd_save_success': 'BDD content saved successfully',
        'bdd_content_required': 'Please enter BDD content',
        'bdd_save_failed': 'Failed to save BDD',
        
        // AAprompt 編輯器
        'aa_editor_title': 'AAprompt Editor',
        'aa_text_label': 'Actor-Action Prompt',
        'aa_text_placeholder': 'Format: As a [role], [action], in the context of [situation], [constraints]\n\nExample:\nAs a natural language ordering system, parse customer order requests and identify menu items, in the context of quick service restaurants, ensure accurate identification of menu items and quantities, handle special requests and modifications',
        'save_aaprompt_btn': 'Save',
        'cannot_open_file_dialog': 'Cannot open file selection dialog, please try clicking the "Select File" button',
        'file_selection_error': 'Error occurred after file selection, please try again',
        'saving_aaprompt': 'Saving...',
        'aaprompt_save_success': 'AAprompt content saved successfully',
        'aaprompt_content_required': 'Please enter AAprompt content',
        'aaprompt_save_failed': 'Failed to save AAprompt',
        
        // APPprompt 生成器
        'app_generator_title': 'APPprompt Generator',
        'app_generator_btn': 'Generate APPprompt',
        'download_appprompt_btn': 'Download Complete APPprompt',
        'generating': 'Generating...',
        'generate_appprompt': 'Generate APPprompt',
        'generating_appprompt_using': 'Generating APPprompt using',
        'generating_appprompt_wait': 'Generating APPprompt, please wait...',
        'content': 'content',
        'analyzing_with_gemini': 'Analyzing and generating APPprompt with USI AIOS...',
        'generated_appprompt': 'Generated APPprompt',
        'generated_appprompt_empty': 'Generated APPprompt is empty',
        'appprompt_generation_error': 'APPprompt generation error',
        'generating_with_gemini': 'Generating APPprompt with USI AIOS...',
        'gemini_generation_success': 'USI AIOS has successfully generated APPprompt',
        'appprompt_generation_success': 'APPprompt generated successfully',
        'appprompt_generation_failed': 'Failed to generate APPprompt',
        'generate_appprompt_first': 'Please generate APPprompt first',
        'found_generate_btn': 'Found generate-btn button, binding click event',
        'generate_btn_not_found': 'Generate-btn button not found',
        'auto_generating_appprompt': 'Auto-generating USI AIOS Agent, please wait...',
        'appprompt_auto_generated': 'USI AIOS Agent has been auto-generated, you can now start ordering!',
        'auto_generate_failed': 'Auto-generation of USI AIOS Agent failed, please manually switch to dev page to generate',
        'please_upload_menu_first': 'Please upload menu first before using ordering function',
        'appprompt_ready': 'USI AIOS Agent is ready, you can start ordering',
        'appprompt_not_ready': 'USI AIOS Agent is not ready yet',
        'system_initializing': 'System is initializing...',
        'auto_generate_on_switch': 'USI AIOS Agent will be auto-generated when switching to this tab...',
        'ready_to_order': 'You can now start using the natural language ordering feature!',
        
        // 自然語言點餐
        'natural_order_title': 'Quick Ordering System',
        'restaurant_selector_label': 'Select Restaurant',
        'order_input_label': 'Enter your order',
        'order_input_placeholder': 'Example: I want a Big Mac meal with Coke instead of the regular drink, and large fries',
        'submit_order_btn': 'Process Order',
        'clear_input_btn': 'Clear Input',
        'voice_input_btn': 'Voice Input',
        'modify_order_btn': 'Modify Order',
        'clear_order_btn': 'Clear Order',
        'confirm_order_btn': 'Confirm Order',
        'restart_order_btn': 'Start Over',
        
        // USI AIOS 訂單助手
        'gemini_assistant_title': 'USI AIOS Order Assistant',
        'ai_processing_title': 'USI AIOS Intelligent Analysis',
        'step_receive_order': 'Receiving Order',
        'step_ai_analysis': 'AI Semantic Analysis',
        'step_menu_matching': 'Menu Matching',
        'step_generate_result': 'Generating Results',
        'ai_thinking_text': 'Understanding your needs...',
        'preparing_results': 'Preparing to present results...',
        
        // 訂單確認模態框
        'order_complete_title': 'Order Complete!',
        'order_complete_message': 'Thank you for your order! Your meal is being prepared.',
        'order_summary_title': 'Order Summary:',
        'order_summary': 'Order Summary',
        'quantity': 'Quantity',
        'total': 'Total',
        'total_amount': 'Total Amount',
        'close_btn': 'Close',
        'new_order_btn': 'New Order',
        
        // 通知訊息
        'success_title': 'Success!',
        'error_title': 'Error!',
        'no_matches_title': 'Unable to get AI response',
        'no_matches_text': 'Please try your order request again',
        'order_confirm_error': 'Order confirmation function is temporarily unavailable, please try again later',
        'no_order_content': 'Unable to get order content',
        'order_summary_failed': 'Order summary generation failed, please check console logs',
        'select_items_first': 'Please select items to add to cart first',
        'order_processing': 'Processing order...',
        'order_submitted': 'Order submitted successfully!',
        'order_submit_failed': 'Order submission failed, please try again',
        'gemini_analysis_success': 'USI AIOS Agent has successfully analyzed your order',
        'bdd_save_success': 'BDD saved successfully!',
        'bdd_save_failed': 'BDD save failed',
        'aaprompt_save_success': 'AAprompt saved successfully!',
        'aaprompt_save_failed': 'AAprompt save failed',
        'error_title': 'Error!',
        'save_bdd_btn': 'Save',
        'save_aaprompt_btn': 'Save',
        'refresh_language_content': 'Refresh Language Content',
        'confirm_refresh_bdd': 'Are you sure you want to update the BDD editor content to {language} default content?\n\nThis will overwrite the current content.',
        'confirm_refresh_aaprompt': 'Are you sure you want to update the AAprompt editor content to {language} default content?\n\nThis will overwrite the current content.',
        'refreshing': 'Refreshing...',
        'refresh_success_bdd': 'BDD editor has been updated to {language} default content',
        'refresh_success_aaprompt': 'AAprompt editor has been updated to {language} default content',
        'refresh_failed_bdd': 'Failed to refresh BDD editor language content',
        'refresh_failed_aaprompt': 'Failed to refresh AAprompt editor language content',
        'unable_load_default_content': 'Unable to load default content',
        'auto_switch_bdd_confirm': 'Detected that you are switching language to {language}.\nWould you like to update the BDD editor content to the new language default content?\n\nClick "OK" to update, "Cancel" to keep current content.',
        'auto_switch_aaprompt_confirm': 'Detected that you are switching language to {language}.\nWould you like to update the AAprompt editor content to the new language default content?\n\nClick "OK" to update, "Cancel" to keep current content.',
        'cannot_open_file_dialog': 'Cannot open file selection dialog, please try clicking the "Select File" button',
        'file_selection_error': 'Error occurred after file selection, please try again',
        
        // Toast 消息
        'new_order_started': 'New order started',
        'order_cleared': 'Order cleared',
        'enter_modification_content': 'Please enter your modification content',
        'cart_empty': 'Cart is empty, please select items first',
        'select_valid_items': 'Please select valid items before checkout',
        'item_increased': 'Increased quantity of {item}',
        'item_decreased': 'Decreased quantity of {item}',
        'item_removed': 'Removed {item}',
        'total_corrected': 'Total amount corrected from NT${original} to NT${final}',
        'menu_upload_success': 'Menu Upload Successful!',
        'processed_items': 'Successfully processed {count} menu items',
        'menu_upload_failed': 'Menu Upload Failed',
        'upload_error': 'Error occurred during upload',
        'file_selected': 'File Selected',
        'file_size': 'File Size',
        'file_selected_success': 'File successfully selected, please click "Upload Menu" button to complete upload',
        'file_name': 'File Name',
        'restaurant_id': 'Restaurant ID',
        'total_items': 'Total Items',
        'category_info': 'Category Information',
        'items_unit': 'items',
        'info_title': 'Information',
        'warning_title': 'Warning',
        
        // 菜單項目顯示
        'menu_item_name': 'Item Name',
        'menu_item_price': 'Price',
        'menu_item_category': 'Category',
        
        // 編輯器錯誤訊息
        'aaprompt_syntax_error': 'AAprompt Syntax Error',
        
        // 菜單預覽
        'menu_preview': 'Menu Preview',
        'version': 'Version',
        'please_select_file': 'Please select a file to upload',
        
        // 語言選單
        'language_btn': 'English',
        'language_zh': '中文',
        'language_en': 'English',
        'language_ja': '日本語',
        
        // 新增的錯誤訊息和處理文字
        'gemini_api_connection_failed': 'Gemini API Connection Failed',
        'check_network_connection': 'Please check your network connection or try again later',
        'processing_please_wait': 'Processing, please wait...',
        'ai_analyzing_order': 'AI is analyzing your order, please wait',
        'gemini_ai_analyzing_order': 'USI AIOS is analyzing your order, please wait',
        'ai_analyzed_order': 'AI has analyzed your order:',
        'enter_modification_request': 'Please enter your modification request',
        'order_cleared_success': 'Order cleared',
        'order_confirmation_unavailable': 'Order confirmation function is temporarily unavailable',
        'order_confirmation_modal_not_found': 'Order confirmation modal not found',
        'unable_to_get_order_info': 'Unable to get order information',
        'please_confirm_price': 'Please confirm price',
        'confirm_order': 'Confirm Order',
        'please_confirm_your_order': 'Please confirm your order',
    'speech_permission_needed': 'Speech permission needed',
    'enable_speech': 'Enable Speech',
    'speech_enabled': 'Speech enabled',
    'speech_disabled': 'Speech disabled',
    'close': 'Close',
        'modify_order': 'Modify Order',
        'clear_order': 'Clear Order',
        'gemini_ai_response': 'USI AIOS Response',
        'order_result_container_not_found': 'Order result container not found',
        'displaying_gemini_response': 'Displaying USI AIOS response',
        'set_gemini_order_data': 'Set window.currentGeminiOrderData',
        'using_fixed_checkout': 'Using fixed checkout function to process USI AIOS response',
        'fixed_checkout_not_found': 'Fixed checkout function not found, using backup confirmation dialog',
        'showing_order_confirmation': 'Showing order confirmation dialog',
        'order_confirmation_modal_shown': 'Order confirmation modal shown',
        'showing_toast_notification': 'Showing toast notification',
        'toast_container_created': 'Toast container created',

        // 會話狀態面板
        'session_id': 'Session ID',
        'menu_status': 'Menu',
        'language_status': 'Language',
        'clear_session_btn': 'Clear Session',
        'status_loaded': 'Loaded',
        'status_not_loaded': 'Not Loaded',
        'status_generated': 'Generated',
        'status_not_generated': 'Not Generated',
        'no_appprompt_available': 'No APPprompt available, please upload menu and generate APPprompt first',

        // 錯誤訊息
        'please_enter_order_request': 'Please enter your order request',
        'session_manager_not_initialized': 'Session manager not initialized, please reload the page',
        'usi_aios_response_format_error': 'USI AIOS response format error',
        'confirm_clear_session': 'Are you sure you want to clear the current session? This will clear all uploaded menus and generated APPprompts.',
        'session_cleared': 'Session cleared'
    },
    'ja-JP': { // 日文
        // 頁面標題
        'page_title': '自然言語注文システム',
        'page_subtitle': 'アーキテクチャエージェントスマート小売アプリケーション',
        
        // 標籤頁
        'tab_menu_management': 'メニュー管理',
        'tab_bdd_editor': 'BDDエディタ',
        'tab_aa_prompt_editor': 'AApromptエディタ',
        'tab_app_prompt_generator': 'APPpromptジェネレータ',
        'tab_natural_order': '自然言語注文',
        
        // 菜單管理
        'menu_management_title': 'メニューアップロードと管理',
        'restaurant_id_label': 'レストランID',
        'restaurant_id_placeholder': 'レストラン識別子を入力してください',
        'drop_file_text': 'ファイルをここにドラッグするか、クリックして選択',
        'supported_formats': '対応フォーマット：CSV、Excel、JSON',
        'select_file_btn': 'ファイル選択',
        'auto_detect_category': 'カテゴリを自動検出',
        'upload_menu_btn': 'メニューをアップロード',
        'processing_text': '処理中、お待ちください...',
        
        // BDD 編輯器
        'bdd_editor_title': 'BDDエディタ',
        'bdd_text_label': '行動駆動開発（BDD）',
        'bdd_text_placeholder': '例：\nFeature: 自然言語注文\n  As a customer\n  I want to order food using natural language\n  So that I can place my order quickly and intuitively\n\nScenario: 顧客がテキスト入力で注文する...',
        'save_bdd_btn': '保存',
        'saving_bdd': '保存中...',
        'bdd_save_success': 'BDD内容が正常に保存されました',
        'bdd_content_required': 'BDD内容を入力してください',
        'bdd_save_failed': 'BDDの保存に失敗しました',
        
        // AAprompt 編輯器
        'aa_editor_title': 'AApromptエディタ',
        'aa_text_label': 'アクター・アクションプロンプト',
        'aa_text_placeholder': '形式：[役割]として、[アクション]、[状況]のコンテキストで、[制約]\n\n例：\n自然言語注文システムとして、顧客の注文リクエストを解析しメニュー項目を識別し、ファストフードレストランのコンテキストで、メニュー項目と数量を正確に識別し、特別なリクエストと変更を処理する',
        'save_aaprompt_btn': '保存',
        'cannot_open_file_dialog': 'ファイル選択ダイアログを開けません。「ファイルを選択」ボタンをクリックしてください',
        'file_selection_error': 'ファイル選択後にエラーが発生しました。もう一度お試しください',
        'saving_aaprompt': '保存中...',
        'aaprompt_save_success': 'AAprompt内容が正常に保存されました',
        'aaprompt_content_required': 'AAprompt内容を入力してください',
        'aaprompt_save_failed': 'AApromptの保存に失敗しました',
        'error_title': 'エラー！',
        'save_bdd_btn': '保存',
        'save_aaprompt_btn': '保存',
        'cannot_open_file_dialog': 'ファイル選択ダイアログを開けません。「ファイルを選択」ボタンをクリックしてください',
        'file_selection_error': 'ファイル選択後にエラーが発生しました。もう一度お試しください',
        
        // APPprompt 生成器
        'app_generator_title': 'APPpromptジェネレータ',
        'app_generator_btn': 'APPpromptを生成',
        'download_appprompt_btn': '完全なAPPpromptをダウンロード',
        'generating': '生成中...',
        'generate_appprompt': 'APPpromptを生成',
        'generating_appprompt_using': 'APPpromptを生成中、使用',
        'generating_appprompt_wait': 'APPpromptを生成中、お待ちください...',
        'content': 'コンテンツ',
        'analyzing_with_gemini': 'USI AIOSでAPPpromptを分析・生成中...',
        'generated_appprompt': '生成されたAPPprompt',
        'generated_appprompt_empty': '生成されたAPPpromptが空です',
        'appprompt_generation_error': 'APPprompt生成エラー',
        'generating_with_gemini': 'USI AIOSでAPPpromptを生成中...',
        'gemini_generation_success': 'USI AIOSがAPPpromptを正常に生成しました',
        'appprompt_generation_success': 'APPpromptが正常に生成されました',
        'appprompt_generation_failed': 'APPpromptの生成に失敗しました',
        'generate_appprompt_first': '最初にAPPpromptを生成してください',
        'found_generate_btn': 'generate-btnボタンが見つかりました、クリックイベントをバインド',
        'generate_btn_not_found': 'generate-btnボタンが見つかりません',
        'auto_generating_appprompt': 'USI AIOS Agentを自動生成中、お待ちください...',
        'appprompt_auto_generated': 'USI AIOS Agentが自動生成されました、注文を開始できます！',
        'auto_generate_failed': 'USI AIOS Agentの自動生成に失敗しました、手動で開発ページに切り替えて生成してください',
        'please_upload_menu_first': '注文機能を使用する前に、まずメニューをアップロードしてください',
        'appprompt_ready': 'USI AIOS Agentの準備ができました、注文を開始できます',
        'appprompt_not_ready': 'USI AIOS Agentはまだ準備できていません',
        'system_initializing': 'システムを初期化中...',
        'auto_generate_on_switch': 'このタブに切り替えると自動的にUSI AIOS Agentが生成されます...',
        'ready_to_order': '自然言語注文機能をご利用いただけます！',
        'generation_process_error': '生成プロセス中にエラーが発生しました',
        
        // 自然語言點餐
        'natural_order_title': 'クイック注文システム',
        'restaurant_selector_label': 'レストランを選択',
        'order_input_label': '注文を入力してください',
        'order_input_placeholder': '例：ビッグマックセットを注文したいです。飲み物はコーラに変更して、ポテトは大きいサイズでお願いします',
        'submit_order_btn': '注文を処理',
        'clear_input_btn': '入力をクリア',
        'voice_input_btn': '音声入力',
        'modify_order_btn': '注文を修正',
        'clear_order_btn': '注文をクリア',
        'confirm_order_btn': '注文を確認',
        'restart_order_btn': '最初からやり直し',
        
        // USI AIOS 訂單助手
        'gemini_assistant_title': 'USI AIOS 注文アシスタント',
        'ai_processing_title': 'USI AIOS インテリジェント分析中',
        'step_receive_order': '注文受付',
        'step_ai_analysis': 'AI 意味解析',
        'step_menu_matching': 'メニューマッチング',
        'step_generate_result': '結果生成',
        'ai_thinking_text': 'ご要望を理解しています...',
        'preparing_results': '結果をご提示する準備をしています...',
        
        // 訂單確認模態框
        'order_complete_title': '注文完了！',
        'order_complete_message': 'ご注文ありがとうございます！お食事を準備中です。',
        'order_summary_title': '注文概要：',
        'order_summary': '注文概要',
        'quantity': '数量',
        'total': '合計',
        'total_amount': '合計金額',
        'close_btn': '閉じる',
        'new_order_btn': '新しい注文',
        
        // 通知訊息
        'success_title': '成功！',
        'error_title': 'エラー！',
        'no_matches_title': 'AIレスポンスを取得できません',
        'no_matches_text': '注文リクエストをもう一度お試しください',
        'order_confirm_error': '注文確認機能は一時的に利用できません。後でもう一度お試しください',
        'no_order_content': '注文内容を取得できません',
        'order_summary_failed': '注文概要の生成に失敗しました。コンソールログを確認してください',
        'select_items_first': '最初にカートに商品を追加してください',
        'order_processing': '注文処理中...',
        'order_submitted': '注文が正常に送信されました！',
        'order_submit_failed': '注文の送信に失敗しました。もう一度お試しください',
        'gemini_analysis_success': 'USI AIOS Agentがご注文を正常に分析しました',
        'bdd_save_success': 'BDDが正常に保存されました！',
        'bdd_save_failed': 'BDDの保存に失敗しました',
        'aaprompt_save_success': 'AApromptが正常に保存されました！',
        'aaprompt_save_failed': 'AApromptの保存に失敗しました',
        'error_title': 'エラー！',
        'save_bdd_btn': '保存',
        'save_aaprompt_btn': '保存',
        'refresh_language_content': '言語コンテンツを更新',
        'confirm_refresh_bdd': 'BDDエディタの内容を{language}のデフォルトコンテンツに更新してもよろしいですか？\n\n現在の内容が上書きされます。',
        'confirm_refresh_aaprompt': 'AApromptエディタの内容を{language}のデフォルトコンテンツに更新してもよろしいですか？\n\n現在の内容が上書きされます。',
        'refreshing': '更新中...',
        'refresh_success_bdd': 'BDDエディタが{language}のデフォルトコンテンツに更新されました',
        'refresh_success_aaprompt': 'AApromptエディタが{language}のデフォルトコンテンツに更新されました',
        'refresh_failed_bdd': 'BDDエディタの言語コンテンツの更新に失敗しました',
        'refresh_failed_aaprompt': 'AApromptエディタの言語コンテンツの更新に失敗しました',
        'unable_load_default_content': 'デフォルトコンテンツを読み込めません',
        'auto_switch_bdd_confirm': '言語を{language}に切り替えていることを検出しました。\nBDDエディタの内容を新しい言語のデフォルトコンテンツに更新しますか？\n\n「OK」をクリックして更新、「キャンセル」で現在の内容を保持します。',
        'auto_switch_aaprompt_confirm': '言語を{language}に切り替えていることを検出しました。\nAApromptエディタの内容を新しい言語のデフォルトコンテンツに更新しますか？\n\n「OK」をクリックして更新、「キャンセル」で現在の内容を保持します。',
        'cannot_open_file_dialog': 'ファイル選択ダイアログを開けません。「ファイルを選択」ボタンをクリックしてください',
        'file_selection_error': 'ファイル選択後にエラーが発生しました。もう一度お試しください',
        
        // Toast 消息
        'new_order_started': '新しい注文を開始しました',
        'order_cleared': '注文がクリアされました',
        'enter_modification_content': '修正内容を入力してください',
        'cart_empty': 'カートが空です。最初に商品を選択してください',
        'select_valid_items': 'チェックアウト前に有効な商品を選択してください',
        'item_increased': '{item}の数量が増加しました',
        'item_decreased': '{item}の数量が減少しました',
        'item_removed': '{item}が削除されました',
        'total_corrected': '合計金額がNT${original}からNT${final}に修正されました',
        'menu_upload_success': 'メニューのアップロードに成功しました！',
        'processed_items': '{count}個のメニュー項目が正常に処理されました',
        'menu_upload_failed': 'メニューのアップロードに失敗しました',
        'upload_error': 'アップロード中にエラーが発生しました',
        'file_selected': 'ファイルが選択されました',
        'file_size': 'ファイルサイズ',
        'file_selected_success': 'ファイルが正常に選択されました。「メニューをアップロード」ボタンをクリックしてアップロードを完了してください',
        'file_name': 'ファイル名',
        'restaurant_id': 'レストランID',
        'total_items': '総商品数',
        'category_info': 'カテゴリ情報',
        'items_unit': '項目',
        'info_title': '情報',
        'warning_title': '警告',
        
        // 菜單項目顯示
        'menu_item_name': '商品名',
        'menu_item_price': '価格',
        'menu_item_category': 'カテゴリー',
        
        // 編輯器錯誤訊息
        'aaprompt_syntax_error': 'AAprompt構文エラー',
        
        // 菜單預覽
        'menu_preview': 'メニュープレビュー',
        'version': 'バージョン',
        'please_select_file': 'アップロードするファイルを選択してください',
        
        // 語言選單
        'language_btn': '日本語',
        'language_zh': '中文',
        'language_en': 'English',
        'language_ja': '日本語',
        
        // 新增的錯誤訊息和處理文字
        'gemini_api_connection_failed': 'Gemini API接続失敗',
        'check_network_connection': 'ネットワーク接続を確認するか、後でもう一度お試しください',
        'processing_please_wait': '処理中、お待ちください...',
        'ai_analyzing_order': 'AIがご注文を分析中です、お待ちください',
        'gemini_ai_analyzing_order': 'USI AIOSがご注文を分析中です、お待ちください',
        'ai_analyzed_order': 'AIがご注文を分析しました：',
        'enter_modification_request': '変更内容を入力してください',
        'order_cleared_success': '注文をクリアしました',
        'order_confirmation_unavailable': '注文確認機能は一時的に利用できません',
        'order_confirmation_modal_not_found': '注文確認モーダルが見つかりません',
        'unable_to_get_order_info': '注文情報を取得できません',
        'please_confirm_price': '価格を確認してください',
        'confirm_order': '注文確認',
        'please_confirm_your_order': 'ご注文をご確認ください',
        'speech_permission_needed': '音声権限が必要です',
        'enable_speech': '音声を有効にする',
        'speech_enabled': '音声が有効になりました',
        'speech_disabled': '音声が無効になりました',
        'close': '閉じる',
        'modify_order': '注文変更',
        'clear_order': '注文クリア',
        'gemini_ai_response': 'USI AIOS応答',
        'order_result_container_not_found': '注文結果コンテナが見つかりません',
        'displaying_gemini_response': 'USI AIOS応答を表示中',
        'set_gemini_order_data': 'window.currentGeminiOrderDataを設定しました',
        'using_fixed_checkout': '修正版チェックアウト機能を使用してUSI AIOS応答を処理',
        'fixed_checkout_not_found': '修正版チェックアウト機能が見つかりません、バックアップ確認ダイアログを使用',
        'showing_order_confirmation': '注文確認ダイアログを表示中',
        'order_confirmation_modal_shown': '注文確認モーダルを表示しました',
        'showing_toast_notification': 'トースト通知を表示中',
        'toast_container_created': 'トーストコンテナを作成しました',

        // 會話狀態面板
        'session_id': 'セッションID',
        'menu_status': 'メニュー',
        'language_status': '言語',
        'clear_session_btn': 'セッションをクリア',
        'status_loaded': '読み込み済み',
        'status_not_loaded': '未読み込み',
        'status_generated': '生成済み',
        'status_not_generated': '未生成',
        'no_appprompt_available': '利用可能なAPPpromptがありません。まずメニューをアップロードしてAPPpromptを生成してください',

        // 錯誤訊息
        'please_enter_order_request': 'ご注文内容を入力してください',
        'session_manager_not_initialized': 'セッションマネージャーが初期化されていません。ページを再読み込みしてください',
        'usi_aios_response_format_error': 'USI AIOS レスポンス形式エラー',
        'confirm_clear_session': '現在のセッションをクリアしてもよろしいですか？これにより、アップロードされたすべてのメニューと生成されたAPPpromptがクリアされます。',
        'session_cleared': 'セッションがクリアされました'
    }
};

// 獲取當前語言
function getCurrentLanguage() {
    return localStorage.getItem('preferredLanguage') || 'zh-TW';
}

// 獲取翻譯
function getTranslation(key) {
    const currentLanguage = getCurrentLanguage();
    if (languageResources[currentLanguage] && languageResources[currentLanguage][key]) {
        return languageResources[currentLanguage][key];
    }
    // 如果找不到翻譯，返回繁體中文版本或鍵值本身
    return (languageResources['zh-TW'] && languageResources['zh-TW'][key]) || key;
}

// 將函數和資源對象設置為全局變量，以便其他文件可以訪問
window.getCurrentLanguage = getCurrentLanguage;
window.getTranslation = getTranslation;
window.languageResources = languageResources;

// 初始化語言選單
function initializeLanguage() {
    // 創建語言切換按鈕
    createLanguageSwitcher();
    
    // 設置語言選單按鈕文本
    const languageBtn = document.getElementById('language-btn');
    if (languageBtn) {
        languageBtn.textContent = getTranslation('language_btn');
    }
    
    // 更新頁面上的所有文本
    updatePageLanguage();
}

// 創建語言切換按鈕
function createLanguageSwitcher() {
    // 檢查是否已經存在語言切換按鈕
    if (document.querySelector('.language-switcher')) {
        return;
    }
    
    // 創建語言切換器容器
    const languageSwitcher = document.createElement('div');
    languageSwitcher.className = 'language-switcher';
    
    // 創建語言按鈕
    const languageBtn = document.createElement('button');
    languageBtn.id = 'language-btn';
    languageBtn.className = 'language-btn';
    languageBtn.innerHTML = '<i class="fas fa-globe globe-icon"></i>' + getTranslation('language_btn');
    
    // 創建語言下拉選單
    const languageDropdown = document.createElement('div');
    languageDropdown.className = 'language-dropdown';
    
    // 添加語言選項
    const zhOption = document.createElement('div');
    zhOption.className = 'language-option';
    zhOption.textContent = getTranslation('language_zh');
    zhOption.onclick = function() { switchLanguage('zh-TW'); };
    
    const enOption = document.createElement('div');
    enOption.className = 'language-option';
    enOption.textContent = getTranslation('language_en');
    enOption.onclick = function() { switchLanguage('en-US'); };
    
    const jaOption = document.createElement('div');
    jaOption.className = 'language-option';
    jaOption.textContent = getTranslation('language_ja');
    jaOption.onclick = function() { switchLanguage('ja-JP'); };
    
    // 組合元素
    languageDropdown.appendChild(zhOption);
    languageDropdown.appendChild(enOption);
    languageDropdown.appendChild(jaOption);
    
    languageSwitcher.appendChild(languageBtn);
    languageSwitcher.appendChild(languageDropdown);
    
    // 添加到頁面頂部
    const header = document.querySelector('header');
    if (header) {
        header.appendChild(languageSwitcher);
    }
    
    // 添加點擊事件處理程序
    languageBtn.addEventListener('click', function(e) {
        e.preventDefault();
        languageDropdown.classList.toggle('show');
    });
    
    // 點擊頁面其他地方時關閉下拉選單
    document.addEventListener('click', function(e) {
        if (!languageSwitcher.contains(e.target)) {
            languageDropdown.classList.remove('show');
        }
    });
}

// 更新頁面語言
function updatePageLanguage() {
    // 更新頁面標題
    document.title = getTranslation('page_title');
    
    // 更新頁面子標題
    const pageSubtitle = document.querySelector('header p');
    if (pageSubtitle) {
        pageSubtitle.textContent = getTranslation('page_subtitle');
    }
    
    // 更新標籤頁 - 修正選擇器，使用div.tab而不是a標籤
    const tabMenuManagement = document.querySelector('div.tab[data-tab="menu-management"]');
    if (tabMenuManagement) {
        tabMenuManagement.textContent = getTranslation('tab_menu_management');
    }
    
    const tabBddEditor = document.querySelector('div.tab[data-tab="bdd-editor"]');
    if (tabBddEditor) {
        tabBddEditor.textContent = getTranslation('tab_bdd_editor');
    }
    
    const tabAaPromptEditor = document.querySelector('div.tab[data-tab="aa-prompt-editor"]');
    if (tabAaPromptEditor) {
        tabAaPromptEditor.textContent = getTranslation('tab_aa_prompt_editor');
    }
    
    const tabAppPromptGenerator = document.querySelector('div.tab[data-tab="app-prompt-generator"]');
    if (tabAppPromptGenerator) {
        tabAppPromptGenerator.textContent = getTranslation('tab_app_prompt_generator');
    }
    
    const tabNaturalOrder = document.querySelector('div.tab[data-tab="natural-order"]');
    if (tabNaturalOrder) {
        tabNaturalOrder.textContent = getTranslation('tab_natural_order');
    }
    
    // 更新頁面標題
    const pageTitle = document.querySelector('header h1');
    if (pageTitle) {
        pageTitle.textContent = getTranslation('page_title');
    }
    
    // 更新菜單管理部分
    const menuManagementTitle = document.querySelector('#menu-management h2');
    if (menuManagementTitle) {
        menuManagementTitle.textContent = getTranslation('menu_management_title');
    }
    
    const restaurantIdLabel = document.querySelector('label[for="restaurant-id"]');
    if (restaurantIdLabel) {
        restaurantIdLabel.textContent = getTranslation('restaurant_id_label');
    }
    
    const restaurantIdInput = document.getElementById('restaurant-id');
    if (restaurantIdInput) {
        restaurantIdInput.placeholder = getTranslation('restaurant_id_placeholder');
    }
    
    const dropFileText = document.querySelector('.drop-area p');
    if (dropFileText) {
        dropFileText.textContent = getTranslation('drop_file_text');
    }
    
    const supportedFormats = document.querySelector('.drop-area small');
    if (supportedFormats) {
        supportedFormats.textContent = getTranslation('supported_formats');
    }
    
    const selectFileBtn = document.getElementById('select-file-btn');
    if (selectFileBtn) {
        selectFileBtn.textContent = getTranslation('select_file_btn');
    }
    
    // 更新所有帶有 data-i18n 屬性的元素
    const i18nElements = document.querySelectorAll('[data-i18n]');
    i18nElements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (key) {
            element.textContent = getTranslation(key);
        }
    });
    
    // 移除重複的聲明 - 刪除以下重複代碼：
    // const dropFileText = document.querySelector('.drop-area p');
    // const supportedFormats = document.querySelector('.drop-area small');
    
    // 更新 BDD 編輯器部分
    const bddEditorTitle = document.querySelector('#bdd-editor h2');
    if (bddEditorTitle) {
        bddEditorTitle.textContent = getTranslation('bdd_editor_title');
    }
    
    const bddTextLabel = document.querySelector('label[for="bdd-text"]');
    if (bddTextLabel) {
        bddTextLabel.textContent = getTranslation('bdd_text_label');
    }
    
    const bddTextarea = document.getElementById('bdd-text');
    if (bddTextarea) {
        bddTextarea.placeholder = getTranslation('bdd_text_placeholder');
    }
    
    const saveBddBtn = document.getElementById('save-bdd');
    if (saveBddBtn) {
        saveBddBtn.textContent = getTranslation('save_bdd_btn');
    }
    
    // 更新 AAprompt 編輯器部分
    const aaEditorTitle = document.querySelector('#aa-prompt-editor h2');
    if (aaEditorTitle) {
        aaEditorTitle.textContent = getTranslation('aa_editor_title');
    }
    
    const aaTextLabel = document.querySelector('label[for="aa-text"]');
    if (aaTextLabel) {
        aaTextLabel.textContent = getTranslation('aa_text_label');
    }
    
    const aaTextarea = document.getElementById('aa-text');
    if (aaTextarea) {
        aaTextarea.placeholder = getTranslation('aa_text_placeholder');
    }
    
    const saveAaPromptBtn = document.getElementById('save-aaprompt');
    if (saveAaPromptBtn) {
        saveAaPromptBtn.textContent = getTranslation('save_aaprompt_btn');
    }
    
    // 更新 APPprompt 生成器部分
    const appGeneratorTitle = document.querySelector('#app-prompt-generator h2');
    if (appGeneratorTitle) {
        appGeneratorTitle.textContent = getTranslation('app_generator_title');
    }
    
    const generateBtn = document.getElementById('generate-btn');
    if (generateBtn) {
        generateBtn.textContent = getTranslation('app_generator_btn');
    }
    
    const downloadBtn = document.getElementById('download-btn');
    if (downloadBtn) {
        downloadBtn.textContent = getTranslation('download_appprompt_btn');
    }
    
    // 更新自然語言點餐部分
    const naturalOrderTitle = document.querySelector('#natural-order h2');
    if (naturalOrderTitle) {
        naturalOrderTitle.textContent = getTranslation('natural_order_title');
    }
    
    const restaurantSelectorLabel = document.querySelector('label[for="restaurant-selector"]');
    if (restaurantSelectorLabel) {
        restaurantSelectorLabel.textContent = getTranslation('restaurant_selector_label');
    }
    
    const orderInputLabel = document.querySelector('label[for="order-input"]');
    if (orderInputLabel) {
        orderInputLabel.textContent = getTranslation('order_input_label');
    }
    
    const orderInput = document.getElementById('order-input');
    if (orderInput) {
        orderInput.placeholder = getTranslation('order_input_placeholder');
    }
    
    const processOrderBtn = document.getElementById('process-order-btn');
    if (processOrderBtn) {
        processOrderBtn.textContent = getTranslation('submit_order_btn');
    }
    
    const clearInputBtn = document.getElementById('clear-input-btn');
    if (clearInputBtn) {
        clearInputBtn.textContent = getTranslation('clear_input_btn');
    }
    
    const voiceInputBtn = document.getElementById('voice-input-btn');
    if (voiceInputBtn) {
        voiceInputBtn.textContent = getTranslation('voice_input_btn');
    }
    
    const modifyOrderBtn = document.getElementById('modify-order-btn');
    if (modifyOrderBtn) {
        modifyOrderBtn.textContent = getTranslation('modify_order_btn');
    }
    
    const clearOrderBtn = document.getElementById('clear-order-btn');
    if (clearOrderBtn) {
        clearOrderBtn.textContent = getTranslation('clear_order_btn');
    }
    
    const confirmOrderBtn = document.getElementById('confirm-order-btn');
    if (confirmOrderBtn) {
        confirmOrderBtn.textContent = getTranslation('confirm_order_btn');
    }
    
    // 更新 Gemini AI 訂單助手標題
    const geminiAssistantTitle = document.getElementById('gemini-assistant-title');
    if (geminiAssistantTitle) {
        geminiAssistantTitle.textContent = getTranslation('gemini_assistant_title');
    }

    // 更新訂單確認模態框
    const totalAmountLabel = document.querySelector('#order-confirmation-modal .total-amount-label');
    if (totalAmountLabel) {
        totalAmountLabel.textContent = getTranslation('total_amount');
    }

    const closeBtn = document.querySelector('#order-confirmation-modal .close-btn');
    if (closeBtn) {
        closeBtn.textContent = getTranslation('close_btn');
    }

    const newOrderBtn = document.getElementById('new-order-btn');
    if (newOrderBtn) {
        newOrderBtn.textContent = getTranslation('new_order_btn');
    }

    // 更新會話狀態面板
    updateSessionStatusLabels();

    // 清除所有已顯示的錯誤和成功訊息
    const messageContainers = [
        'order-success', 'order-error',           // 自然語言點餐的訊息
        'success-message', 'error-message',       // 菜單管理的訊息
        'bdd-success-message', 'bdd-error-message', // BDD編輯器的訊息
        'aa-success-message', 'aa-error-message',   // AAprompt編輯器的訊息
        'generate-success', 'generate-error'        // APPprompt生成器的訊息
    ];

    messageContainers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container && container.style.display !== 'none') {
            // 檢查是否是USI AIOS相關的成功訊息，需要更新而不是清除
            if (containerId === 'order-success' && container.innerHTML.includes('USI AIOS')) {
                container.innerHTML = '✅ ' + getTranslation('gemini_analysis_success');
            } else {
                // 其他訊息都清除，避免語言不匹配
                container.style.display = 'none';
                if (container.innerHTML) {
                    container.innerHTML = '';
                }
            }
        }
    });

    // 更新APPprompt狀態提示（如果在自然語言點餐頁籤）
    const naturalOrderTab = document.getElementById('natural-order');
    if (naturalOrderTab && naturalOrderTab.classList.contains('active')) {
        // 延遲更新狀態提示，確保翻譯已載入
        setTimeout(() => {
            if (typeof updateAPPPromptStatusIndicator === 'function') {
                updateAPPPromptStatusIndicator();
            }
        }, 50);
    }
}

// 更新會話狀態面板的標籤文字
function updateSessionStatusLabels() {
    // 更新會話ID標籤
    const sessionIdLabel = document.querySelector('.session-id');
    if (sessionIdLabel) {
        const sessionIdSpan = sessionIdLabel.querySelector('#current-session-id');
        const currentSessionId = sessionIdSpan ? sessionIdSpan.textContent : '-';
        sessionIdLabel.innerHTML = `${getTranslation('session_id')}: <span id="current-session-id" style="font-family: monospace; color: #007bff;">${currentSessionId}</span>`;
    }

    // 更新菜單指示器
    const menuIndicator = document.getElementById('menu-indicator');
    if (menuIndicator) {
        const statusElement = menuIndicator.querySelector('.status');
        const currentStatus = statusElement ? statusElement.textContent : '';
        const currentColor = statusElement ? statusElement.style.color : '';
        menuIndicator.innerHTML = `📋 ${getTranslation('menu_status')}: <span class="status" style="font-weight: bold; color: ${currentColor};">${currentStatus}</span>`;
    }

    // 更新APPprompt指示器
    const promptIndicator = document.getElementById('prompt-indicator');
    if (promptIndicator) {
        const statusElement = promptIndicator.querySelector('.status');
        const currentStatus = statusElement ? statusElement.textContent : '';
        const currentColor = statusElement ? statusElement.style.color : '';
        promptIndicator.innerHTML = `🤖 APPprompt: <span class="status" style="font-weight: bold; color: ${currentColor};">${currentStatus}</span>`;
    }

    // 更新語言指示器
    const languageIndicator = document.getElementById('language-indicator');
    if (languageIndicator) {
        const statusElement = languageIndicator.querySelector('.status');
        const currentStatus = statusElement ? statusElement.textContent : '';
        const currentColor = statusElement ? statusElement.style.color : '';
        languageIndicator.innerHTML = `🌐 ${getTranslation('language_status')}: <span class="status" style="font-weight: bold; color: ${currentColor};">${currentStatus}</span>`;
    }

    // 更新清除會話按鈕
    const clearSessionBtn = document.getElementById('clear-session-btn');
    if (clearSessionBtn) {
        clearSessionBtn.textContent = getTranslation('clear_session_btn');
    }
}

// 切換語言
function switchLanguage(language) {
    console.log('開始切換語言到:', language);
    localStorage.setItem('preferredLanguage', language);

    // 更新會話管理器的語言設定（這會觸發APPprompt語言匹配檢查）
    if (window.sessionManager) {
        window.sessionManager.setLanguage(language);
    }

    initializeLanguage();

    // 更新語音識別語言設定
    if (window.speechRecognitionInstance) {
        if (language === 'en-US') {
            window.speechRecognitionInstance.lang = 'en-US';
        } else if (language === 'ja-JP') {
            window.speechRecognitionInstance.lang = 'ja-JP';
        } else {
            window.speechRecognitionInstance.lang = 'zh-TW';
        }
    }

    // 延遲更新動態生成的內容
    setTimeout(() => {
        updatePageLanguage();
    }, 100);

    // 更新語音切換按鈕文字
    if (typeof updateSpeechToggleButton === 'function') {
        updateSpeechToggleButton();
    }

    // 立即更新編輯器內容 - 使用智能更新機制確保編輯器自動刷新
    console.log('立即開始智能更新編輯器語言內容...');
    console.log('檢查編輯器全局變量狀態:');
    console.log('window.bddEditor:', !!window.bddEditor);
    console.log('window.aaPromptEditor:', !!window.aaPromptEditor);

    // 立即執行智能更新，不等待延遲
    if (typeof smartUpdateEditorsLanguage === 'function') {
        try {
            smartUpdateEditorsLanguage(language);
            console.log('已觸發智能編輯器語言更新');
        } catch (error) {
            console.error('觸發智能更新編輯器語言時發生錯誤:', error);
        }
    } else {
        console.error('smartUpdateEditorsLanguage 函數不存在');
    }

    // 等待編輯器初始化的機制
    const waitForEditorsAndUpdate = async (retryCount = 0) => {
        console.log(`等待編輯器初始化，嘗試次數: ${retryCount + 1}`);
        console.log('當前編輯器狀態:');
        console.log('window.bddEditor:', !!window.bddEditor);
        console.log('window.aaPromptEditor:', !!window.aaPromptEditor);

        if (window.bddEditor && window.aaPromptEditor) {
            console.log('編輯器已初始化，執行智能更新...');
            if (typeof smartUpdateEditorsLanguage === 'function') {
                try {
                    await smartUpdateEditorsLanguage(language);
                    console.log('智能編輯器語言更新完成');
                } catch (error) {
                    console.error('智能更新編輯器語言時發生錯誤:', error);

                    // 如果智能更新失敗，回退到強制更新
                    console.log('回退到強制更新機制...');
                    if (typeof forceUpdateEditorsLanguage === 'function') {
                        try {
                            await forceUpdateEditorsLanguage(language);
                            console.log('強制更新編輯器完成');
                        } catch (forceError) {
                            console.error('強制更新編輯器失敗:', forceError);
                        }
                    }
                }
            }
        } else if (retryCount < 10) {
            // 如果編輯器還沒初始化，等待一段時間後重試
            console.log('編輯器尚未初始化，500ms後重試...');
            setTimeout(() => waitForEditorsAndUpdate(retryCount + 1), 500);
        } else {
            console.error('編輯器初始化超時，無法執行語言更新');
        }
    };

    // 延遲更新作為備用機制
    setTimeout(() => {
        console.log('執行備用編輯器語言更新...');
        waitForEditorsAndUpdate();

        console.log('編輯器語言更新流程完成');

        // 移除備用刷新機制，避免重複刷新
    }, 200); // 增加延遲時間確保編輯器準備就緒
}

// 在頁面加載完成後初始化語言選單
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
    
    // 覆蓋原始的showToastMessage函數，以支持多語言
    const originalShowToastMessage = window.showToastMessage;
    if (originalShowToastMessage) {
        window.showToastMessage = function(message, type = 'success') {
            const currentLanguage = getCurrentLanguage();
            
            // 檢查是否為預定義的訊息鍵值或包含預定義的鍵值
            let translatedMessage = message;
            
            // 處理常見的 toast 消息
            if (message === '已開始新的訂單') {
                translatedMessage = getTranslation('new_order_started');
            } else if (message === '訂單已清空') {
                translatedMessage = getTranslation('order_cleared');
            } else if (message === '請輸入您想要修改的內容') {
                translatedMessage = getTranslation('enter_modification_content');
            } else if (message === '購物車是空的，請先選擇餐點') {
                translatedMessage = getTranslation('cart_empty');
            } else if (message === '請先選擇有效的餐點再結帳') {
                translatedMessage = getTranslation('select_valid_items');
            } else if (message.includes('已增加') && message.includes('的數量')) {
                const itemName = message.replace('已增加 ', '').replace(' 的數量', '');
                translatedMessage = getTranslation('item_increased').replace('{item}', itemName);
            } else if (message.includes('已減少') && message.includes('的數量')) {
                const itemName = message.replace('已減少 ', '').replace(' 的數量', '');
                translatedMessage = getTranslation('item_decreased').replace('{item}', itemName);
            } else if (message.includes('已移除')) {
                const itemName = message.replace('已移除 ', '');
                translatedMessage = getTranslation('item_removed').replace('{item}', itemName);
            } else if (message.includes('已修正總金額從')) {
                const match = message.match(/已修正總金額從 NT\$(\d+) 到 NT\$(\d+)/);
                if (match) {
                    translatedMessage = getTranslation('total_corrected')
                        .replace('{original}', match[1])
                        .replace('{final}', match[2]);
                }
            } else if (message.includes('<strong>菜單上傳成功！</strong>')) {
                const itemCount = message.match(/已成功處理 (\d+) 個菜單項目/)?.[1] || '';
                translatedMessage = `<strong>${getTranslation('menu_upload_success')}</strong><br>${getTranslation('processed_items').replace('{count}', itemCount)}`;
            } else if (message.includes('<strong>菜單上傳失敗</strong>')) {
                const errorMsg = message.split('<br>')[1] || '';
                translatedMessage = `<strong>${getTranslation('menu_upload_failed')}</strong><br>${errorMsg}`;
            } else if (message.includes('<strong>上傳過程中發生錯誤</strong>')) {
                const errorMsg = message.split('<br>')[1] || '';
                translatedMessage = `<strong>${getTranslation('upload_error')}</strong><br>${errorMsg}`;
            } else if (message.includes('<strong>錯誤</strong>')) {
                const errorMsg = message.split('<br>')[1] || '';
                translatedMessage = `<strong>${getTranslation('error_title')}</strong><br>${errorMsg}`;
            } else if (message.includes('<strong>成功</strong>')) {
                const successMsg = message.split('<br>')[1] || '';
                translatedMessage = `<strong>${getTranslation('success_title')}</strong><br>${successMsg}`;
            }
            
            // 根據消息類型翻譯標題
            let title = '';
            if (type === 'success') {
                title = getTranslation('success_title');
            } else if (type === 'error') {
                title = getTranslation('error_title');
            } else if (type === 'info') {
                title = getTranslation('info_title');
            } else if (type === 'warning') {
                title = getTranslation('warning_title');
            }
            
            // 調用原始函數，但使用翻譯後的標題和訊息
            originalShowToastMessage(translatedMessage, type, title);
        };
    }
});