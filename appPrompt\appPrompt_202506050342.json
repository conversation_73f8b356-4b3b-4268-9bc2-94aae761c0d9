{"prompt": "```json\n{\n  \"prompt\": \"你是一個自然語言點餐系統，請根據顧客的語音或文字輸入，協助他們完成點餐。你需要理解顧客的需求，例如新增、修改、刪除餐點，以及查詢餐點資訊。如果顧客的輸入不明確或餐點不存在，請提供協助或建議。最後，確認顧客的訂單或取消訂單。\",\n  \"parameters\": {\n    \"feature\": \"自然語言點餐\",\n    \"scenario\": \"顧客取消訂單\",\n    \"given\": [\n      \"顧客位於點餐頁面\",\n      \"系統已顯示識別出的餐點以供確認\",\n      \"系統已顯示最終訂單以供確認 (大麥克套餐, 可樂)\",\n      \"系統已顯示識別出的餐點以供確認 (大麥克套餐, 玉米湯)\"\n    ],\n    \"when\": \"顧客說 \\\"取消\\\" 或 \\\"我不要了\\\"\",\n    \"then\": \"系統應取消目前的訂單，並顯示取消訊息 (例如：\\\"好的，您的訂單已取消。\\\")\",\n    \"menu\": [\n      {\n        \"category\": \"套餐\",\n        \"items\": [\n          {\"id\": \"1\", \"name_zh\": \"大麥克套餐\", \"price\": 143, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?\"},\n          {\"id\": \"11\", \"name_zh\": \"四盎司牛肉堡套餐\", \"price\": 157, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1104.png?\"},\n          {\"id\": \"18\", \"name_zh\": \"帕瑪森主廚雞堡套餐\", \"price\": 192, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41385.png?\"},\n          {\"id\": \"17\", \"name_zh\": \"帕瑪森安格斯牛肉堡套餐\", \"price\": 192, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1102.png?\"},\n          {\"id\": \"7\", \"name_zh\": \"勁辣鷄腿堡套餐\", \"price\": 143, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31373.png?\"},\n          {\"id\": \"6\", \"name_zh\": \"麥克雞塊(10塊)套餐\", \"price\": 174, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41350.png?\"},\n          {\"id\": \"5\", \"name_zh\": \"麥克雞塊(6塊)套餐\", \"price\": 133, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31371.png?\"},\n          {\"id\": \"10\", \"name_zh\": \"麥香魚套餐\", \"price\": 117, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1229.png?\"},\n          {\"id\": \"4\", \"name_zh\": \"麥香雞套餐\", \"price\": 113, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31368.png?\"},\n          {\"id\": \"8\", \"name_zh\": \"麥脆雞(2塊)套餐\", \"price\": 191, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?\"},\n          {\"id\": \"3\", \"name_zh\": \"嫩煎鷄腿堡\", \"price\": 148, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41322.png?\"},\n          {\"id\": \"15\", \"name_zh\": \"蕈菇安格斯牛肉堡套餐\", \"price\": 197, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1088.png?\"},\n          {\"id\": \"2\", \"name_zh\": \"雙層牛肉吉事堡套餐\", \"price\": 137, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1098.png?\"},\n          {\"id\": \"12\", \"name_zh\": \"雙層四盎司牛肉堡套餐\", \"price\": 197, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1105.png?\"},\n          {\"id\": \"9\", \"name_zh\": \"雙層麥香雞套餐\", \"price\": 143, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41384.png?\"},\n          {\"id\": \"16\", \"name_zh\": \"蘑菇主廚雞腿堡套餐\", \"price\": 197, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41389.png?\"},\n          {\"id\": \"13\", \"name_zh\": \"BLT安格斯牛肉堡套餐\", \"price\": 187, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1086.png?\"},\n          {\"id\": \"14\", \"name_zh\": \"BLT嫩煎鷄腿堡套餐\", \"price\": 187, \"image\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41334.png?\"}\n        ]\n      },\n      {\n        \"category\": \"漢堡\",\n        \"items\": [\n          // ... 漢堡類別的菜單項，格式與套餐相同\n        ]\n      }\n      // ... 其他類別\n    ]\n  }\n}\n```\n\n**說明:**\n\n*  `prompt` 提供了系統的角色和目標。\n*  `parameters` 包含了從 BDD 規範中提取的信息，包括功能、場景、給定條件、用戶操作和預期結果。\n*  `menu`  包含了完整的菜單數據，按照指定的格式組織，包含 category 和 items 兩個屬性。每個 item 包含 id, name_zh, price, image 等信息。\n*  由於篇幅限制，漢堡類別的菜單項省略了，實際應用中需要完整填寫。其他類別也需要根據實際情況添加。\n\n\n這個 JSON 格式的 APPprompt 可以直接用於應用程序中，驅動自然語言點餐系統的行為。  這個例子專注於「顧客取消訂單」這個場景，其他場景需要根據 BDD 規範生成對應的 APPprompt。", "parameters": {"menu": [{"category": "套餐", "items": [{"id": "1", "name_zh": "大麥克套餐", "name_en": "Big Mac Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "ビッグマック® セット", "price_jp": 750, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?"}, {"id": "11", "name_zh": "四盎司牛肉堡套餐", "name_en": "Quarter Pounder with Cheese Extra Value Meal", "price": 157, "category": "套餐", "name_jp": "クォーターパウンダー チーズ セット", "price_jp": 780, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1104.png?"}, {"id": "18", "name_zh": "帕瑪森主廚雞堡套餐", "name_en": "Parmesan Chef Chicken Burger Extra Value Meal", "price": 192, "category": "套餐", "name_jp": "チキン パルメザン セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41385.png?"}, {"id": "17", "name_zh": "帕瑪森安格斯牛肉堡套餐", "name_en": "Parmesan Angus Beef Burger Extra Value Meal", "price": 192, "category": "套餐", "name_jp": "アンガスビーフ パルメザン セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1102.png?"}, {"id": "7", "name_zh": "勁辣鷄腿堡套餐", "name_en": "Spicy Chicken Burger Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "スパイシーチキンバーガー エクストラバリューセット", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31373.png?"}, {"id": "6", "name_zh": "麥克雞塊(10塊)套餐", "name_en": "McNuggets (10 pieces) Extra Value Meal", "price": 174, "category": "套餐", "name_jp": "チキンマックナゲット 10ピース セット", "price_jp": 740, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41350.png?"}, {"id": "5", "name_zh": "麥克雞塊(6塊)套餐", "name_en": "McNuggets (6 pieces) Extra Value Meal", "price": 133, "category": "套餐", "name_jp": "チキンマックナゲット 6ピース セット", "price_jp": 650, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31371.png?"}, {"id": "10", "name_zh": "麥香魚套餐", "name_en": "Filet-O-Fish Extra Value Meal", "price": 117, "category": "套餐", "name_jp": "フィレオフィッシュ セット", "price_jp": 680, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1229.png?"}, {"id": "4", "name_zh": "麥香雞套餐", "name_en": "McChicken Extra Value Meal", "price": 113, "category": "套餐", "name_jp": "マックチキン セット", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31368.png?"}, {"id": "8", "name_zh": "麥脆雞(2塊)套餐", "name_en": "McCrispy Chicken (2 pieces) Extra Value Meal", "price": 191, "category": "套餐", "name_jp": "マックフライドチキン 2ピース セット", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?"}, {"id": "3", "name_zh": "嫩煎鷄腿堡", "name_en": "Grilled Chicken Burger Extra Value Meal", "price": 148, "category": "套餐", "name_jp": "グリルドチキンバーガー セット", "price_jp": 690, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41322.png?"}, {"id": "15", "name_zh": "蕈菇安格斯牛肉堡套餐", "name_en": "Mushroom Angus Beef Burger Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "アンガスビーフ マッシュルーム セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1088.png?"}, {"id": "2", "name_zh": "雙層牛肉吉事堡套餐", "name_en": "Double Cheeseburger Extra Value Meal", "price": 137, "category": "套餐", "name_jp": "ダブルチーズバーガー セット", "price_jp": 700, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1098.png?"}, {"id": "12", "name_zh": "雙層四盎司牛肉堡套餐", "name_en": "Double Quarter Pounder with Cheese Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "ダブルクォーターパウンダー チーズ セット", "price_jp": 980, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1105.png?"}, {"id": "9", "name_zh": "雙層麥香雞套餐", "name_en": "Double McChicken Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "倍マックチキン セット", "price_jp": 620, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41384.png?"}, {"id": "16", "name_zh": "蘑菇主廚雞腿堡套餐", "name_en": "Mushroom Chef Chicken Burger Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "チキン マッシュルーム セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41389.png?"}, {"id": "13", "name_zh": "BLT安格斯牛肉堡套餐", "name_en": "BLT Angus Beef Burger Extra Value Meal", "price": 187, "category": "套餐", "name_jp": "アンガスビーフ BLT セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1086.png?"}, {"id": "14", "name_zh": "BLT嫩煎鷄腿堡套餐", "name_en": "BLT Grilled Chicken Burger Extra Value Meal", "price": 187, "category": "套餐", "name_jp": "グリルドチキン BLT セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41334.png?"}]}, {"category": "漢堡", "items": [{"id": "19", "name_zh": "大麥克", "name_en": "Big Mac Single", "price": 78, "category": "漢堡", "name_jp": "ビッグマック®", "price_jp": 410, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?"}, {"id": "29", "name_zh": "四盎司牛肉堡", "name_en": "Quarter Pounder with Cheese Single", "price": 92, "category": "漢堡", "name_jp": "クォーターパウンダー チーズ", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1104.png?"}, {"id": "36", "name_zh": "帕瑪森主廚雞堡", "name_en": "Parmesan Chef Chicken Burger Single", "price": 127, "category": "漢堡", "name_jp": "チキン パルメザン", "price_jp": 700, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41385.png?"}, {"id": "35", "name_zh": "帕瑪森安格斯牛肉堡", "name_en": "Parmesan Angus Beef Burger Single", "price": 127, "category": "漢堡", "name_jp": "アンガスビーフ パルメザン", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1102.png?"}, {"id": "25", "name_zh": "勁辣鷄腿堡", "name_en": "Spicy Chicken Burger Single", "price": 78, "category": "漢堡", "name_jp": "マックチキン", "price_jp": 420, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31373.png?"}, {"id": "24", "name_zh": "麥克雞塊(10塊)", "name_en": "McNuggets (10 pieces) Single", "price": 109, "category": "漢堡", "name_jp": "チキンマックナゲット 10ピース", "price_jp": 580, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/41350.png"}, {"id": "23", "name_zh": "麥克雞塊(6塊)", "name_en": "McNuggets (6 pieces) Single", "price": 68, "category": "漢堡", "name_jp": "チキンマックナゲット 6ピース", "price_jp": 380, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31371.png?"}, {"id": "28", "name_zh": "麥香魚", "name_en": "Filet-O-Fish Single", "price": 52, "category": "漢堡", "name_jp": "フィレオフィッシュ", "price_jp": 300, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/1229.png"}, {"id": "22", "name_zh": "麥香雞", "name_en": "McChicken Single", "price": 48, "category": "漢堡", "name_jp": "マックチキン", "price_jp": 240, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31368.png?"}, {"id": "26", "name_zh": "麥脆雞(2塊)", "name_en": "Crispy Chicken (2 pieces) Single", "price": 126, "category": "漢堡", "name_jp": "マックフライドチキン 2ピース", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?"}, {"id": "21", "name_zh": "嫩煎鷄腿堡", "name_en": "Grilled Chicken Burger Single", "price": 83, "category": "漢堡", "name_jp": "グリルドチキンバーガー", "price_jp": 430, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41322.png?"}, {"id": "33", "name_zh": "蕈菇安格斯牛肉堡", "name_en": "Mushroom Angus Beef Burger Single", "price": 132, "category": "漢堡", "name_jp": "アンガスビーフ マッシュルーム", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1088.png?"}, {"id": "20", "name_zh": "雙層牛肉吉事堡", "name_en": "Double Cheeseburger Single", "price": 72, "category": "漢堡", "name_jp": "ダブルチーズバーガー", "price_jp": 400, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1098.png?"}, {"id": "30", "name_zh": "雙層四盎司牛肉堡", "name_en": "Double Quarter Pounder with Cheese Single", "price": 132, "category": "漢堡", "name_jp": "ダブルクォーターパウンダー チーズ", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1105.png?"}, {"id": "27", "name_zh": "雙層麥香雞", "name_en": "Double McChicken Single", "price": 78, "category": "漢堡", "name_jp": "倍マックチキン", "price_jp": 420, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41384.png?"}, {"id": "34", "name_zh": "蘑菇主廚雞腿堡", "name_en": "Mushroom Chef Chicken Burger Single", "price": 132, "category": "漢堡", "name_jp": "チキン マッシュルーム", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41389.png?"}, {"id": "31", "name_zh": "BLT安格斯牛肉堡", "name_en": "BLT Angus Beef Burger Single", "price": 122, "category": "漢堡", "name_jp": "アンガスビーフ BLT", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1086.png?"}, {"id": "32", "name_zh": "BLT嫩煎鷄腿堡", "name_en": "BLT Grilled Chicken Burger Single", "price": 122, "category": "漢堡", "name_jp": "グリルドチキン BLT", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41334.png?"}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-05T03:42:20.280Z", "aiGenerated": true}}